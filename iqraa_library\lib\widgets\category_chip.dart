import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';

class CategoryChip extends StatelessWidget {
  final String category;
  final int? count;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showIcon;
  final bool showCount;

  const CategoryChip({
    super.key,
    required this.category,
    this.count,
    this.isSelected = false,
    this.onTap,
    this.showIcon = true,
    this.showCount = true,
  });

  @override
  Widget build(BuildContext context) {
    final categoryColor = AppHelpers.getCategoryColor(category);
    final categoryIcon = AppHelpers.getCategoryIcon(category);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: AppConstants.animationDurationShort,
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? categoryColor 
              : categoryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          border: Border.all(
            color: isSelected 
                ? categoryColor 
                : categoryColor.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected 
              ? AppHelpers.createShadow(
                  color: categoryColor.withOpacity(0.3),
                  blurRadius: 8,
                )
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة التصنيف
            if (showIcon) ...[
              Icon(
                categoryIcon,
                color: isSelected 
                    ? Colors.white 
                    : categoryColor,
                size: AppConstants.iconSizeSmall,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
            ],
            
            // اسم التصنيف
            Text(
              category,
              style: TextStyle(
                color: isSelected 
                    ? Colors.white 
                    : categoryColor,
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: isSelected 
                    ? FontWeight.bold 
                    : FontWeight.w500,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
            
            // عدد الكتب
            if (showCount && count != null) ...[
              const SizedBox(width: AppConstants.paddingSmall),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Colors.white.withOpacity(0.2)
                      : categoryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    color: isSelected 
                        ? Colors.white 
                        : categoryColor,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class CategoryGrid extends StatelessWidget {
  final List<String> categories;
  final Map<String, int>? categoriesCount;
  final String? selectedCategory;
  final Function(String)? onCategorySelected;
  final int crossAxisCount;
  final double childAspectRatio;

  const CategoryGrid({
    super.key,
    required this.categories,
    this.categoriesCount,
    this.selectedCategory,
    this.onCategorySelected,
    this.crossAxisCount = 2,
    this.childAspectRatio = 3.0,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: AppConstants.paddingSmall,
        mainAxisSpacing: AppConstants.paddingSmall,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final count = categoriesCount?[category];
        final isSelected = selectedCategory == category;

        return CategoryChip(
          category: category,
          count: count,
          isSelected: isSelected,
          onTap: () => onCategorySelected?.call(category),
        );
      },
    );
  }
}

class CategoryList extends StatelessWidget {
  final List<String> categories;
  final Map<String, int>? categoriesCount;
  final String? selectedCategory;
  final Function(String)? onCategorySelected;
  final ScrollDirection scrollDirection;
  final bool showAllOption;

  const CategoryList({
    super.key,
    required this.categories,
    this.categoriesCount,
    this.selectedCategory,
    this.onCategorySelected,
    this.scrollDirection = Axis.horizontal,
    this.showAllOption = true,
  });

  @override
  Widget build(BuildContext context) {
    final allCategories = showAllOption ? ['الكل', ...categories] : categories;

    if (scrollDirection == Axis.horizontal) {
      return SizedBox(
        height: 40,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: allCategories.length,
          itemBuilder: (context, index) {
            final category = allCategories[index];
            final count = category == 'الكل' 
                ? categoriesCount?.values.fold<int>(0, (sum, count) => sum + count)
                : categoriesCount?[category];
            final isSelected = selectedCategory == category || 
                (selectedCategory == null && category == 'الكل');

            return Padding(
              padding: EdgeInsets.only(
                right: index == 0 ? 0 : AppConstants.paddingSmall,
              ),
              child: CategoryChip(
                category: category,
                count: count,
                isSelected: isSelected,
                onTap: () => onCategorySelected?.call(
                  category == 'الكل' ? '' : category,
                ),
                showIcon: category != 'الكل',
              ),
            );
          },
        ),
      );
    } else {
      return Column(
        children: allCategories.map((category) {
          final count = category == 'الكل' 
              ? categoriesCount?.values.fold<int>(0, (sum, count) => sum + count)
              : categoriesCount?[category];
          final isSelected = selectedCategory == category || 
              (selectedCategory == null && category == 'الكل');

          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
            child: SizedBox(
              width: double.infinity,
              child: CategoryChip(
                category: category,
                count: count,
                isSelected: isSelected,
                onTap: () => onCategorySelected?.call(
                  category == 'الكل' ? '' : category,
                ),
                showIcon: category != 'الكل',
              ),
            ),
          );
        }).toList(),
      );
    }
  }
}
