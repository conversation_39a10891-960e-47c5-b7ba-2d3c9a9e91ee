import 'package:json_annotation/json_annotation.dart';

part 'book.g.dart';

@JsonSerializable()
class Book {
  final String id;
  final String title;
  final String author;
  final String description;
  final String category;
  final List<String> tags;
  final String coverImagePath;
  final String pdfPath;
  final DateTime dateAdded;
  final bool isFavorite;
  final ReadingStatus readingStatus;
  final int totalPages;
  final int currentPage;
  final double rating;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.category,
    required this.tags,
    required this.coverImagePath,
    required this.pdfPath,
    required this.dateAdded,
    this.isFavorite = false,
    this.readingStatus = ReadingStatus.wantToRead,
    this.totalPages = 0,
    this.currentPage = 0,
    this.rating = 0.0,
  });

  factory Book.fromJson(Map<String, dynamic> json) => _$BookFromJson(json);
  Map<String, dynamic> toJson() => _$BookToJson(this);

  Book copyWith({
    String? id,
    String? title,
    String? author,
    String? description,
    String? category,
    List<String>? tags,
    String? coverImagePath,
    String? pdfPath,
    DateTime? dateAdded,
    bool? isFavorite,
    ReadingStatus? readingStatus,
    int? totalPages,
    int? currentPage,
    double? rating,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      description: description ?? this.description,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      pdfPath: pdfPath ?? this.pdfPath,
      dateAdded: dateAdded ?? this.dateAdded,
      isFavorite: isFavorite ?? this.isFavorite,
      readingStatus: readingStatus ?? this.readingStatus,
      totalPages: totalPages ?? this.totalPages,
      currentPage: currentPage ?? this.currentPage,
      rating: rating ?? this.rating,
    );
  }

  double get readingProgress {
    if (totalPages == 0) return 0.0;
    return currentPage / totalPages;
  }

  bool get isCompleted => readingStatus == ReadingStatus.completed;
  bool get isReading => readingStatus == ReadingStatus.reading;
}

@JsonEnum()
enum ReadingStatus {
  @JsonValue('want_to_read')
  wantToRead,
  @JsonValue('reading')
  reading,
  @JsonValue('completed')
  completed,
}

extension ReadingStatusExtension on ReadingStatus {
  String get displayName {
    switch (this) {
      case ReadingStatus.wantToRead:
        return 'أرغب بالقراءة';
      case ReadingStatus.reading:
        return 'جارٍ القراءة';
      case ReadingStatus.completed:
        return 'مقروء';
    }
  }

  String get arabicName {
    switch (this) {
      case ReadingStatus.wantToRead:
        return 'أرغب بالقراءة';
      case ReadingStatus.reading:
        return 'جارٍ القراءة';
      case ReadingStatus.completed:
        return 'مقروء';
    }
  }
}
