import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';
import '../services/book_service.dart';
import '../services/pdf_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';

class PdfReaderScreen extends StatefulWidget {
  final Book book;

  const PdfReaderScreen({
    super.key,
    required this.book,
  });

  @override
  State<PdfReaderScreen> createState() => _PdfReaderScreenState();
}

class _PdfReaderScreenState extends State<PdfReaderScreen> {
  final BookService _bookService = BookService.instance;
  final PdfService _pdfService = PdfService.instance;
  final StorageService _storage = StorageService.instance;

  late PdfViewerController _pdfController;
  late Book _book;
  
  bool _isControlsVisible = true;
  bool _isLoading = true;
  bool _isNightMode = false;
  bool _isFullScreen = false;
  double _zoomLevel = 1.0;
  int _currentPage = 1;
  int _totalPages = 0;
  List<BookmarkPage> _bookmarks = [];
  
  // إعدادات القارئ
  PdfScrollDirection _scrollDirection = PdfScrollDirection.vertical;
  PdfPageLayoutMode _layoutMode = PdfPageLayoutMode.single;
  
  // متغيرات التحكم في الوقت
  DateTime? _sessionStartTime;
  int _readingTimeMinutes = 0;

  @override
  void initState() {
    super.initState();
    _book = widget.book;
    _pdfController = PdfViewerController();
    _sessionStartTime = DateTime.now();
    _initializeReader();
  }

  @override
  void dispose() {
    _saveReadingSession();
    _pdfController.dispose();
    super.dispose();
  }

  Future<void> _initializeReader() async {
    try {
      // تحميل الإشارات المرجعية
      final bookmarks = await _pdfService.getBookmarks(_book.id);
      
      // تحميل آخر صفحة مقروءة
      final lastPage = await _pdfService.getLastReadPage(_book.id);
      
      setState(() {
        _bookmarks = bookmarks;
        _currentPage = lastPage > 0 ? lastPage : 1;
        _isLoading = false;
      });

      // الانتقال إلى آخر صفحة مقروءة
      if (lastPage > 0) {
        _pdfController.jumpToPage(lastPage);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في تحميل الكتاب');
    }
  }

  Future<void> _saveReadingSession() async {
    if (_sessionStartTime != null) {
      final sessionDuration = DateTime.now().difference(_sessionStartTime!);
      final additionalMinutes = sessionDuration.inMinutes;
      
      if (additionalMinutes > 0) {
        await _pdfService.updateReadingTime(_book.id, additionalMinutes);
        await _bookService.updateReadingProgress(
          _book.id,
          _currentPage,
          _totalPages,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isNightMode ? Colors.black : Colors.white,
      appBar: _isFullScreen ? null : _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _isFullScreen ? null : _buildBottomBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _book.title,
        style: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      backgroundColor: _isNightMode 
          ? Colors.grey[900] 
          : AppConstants.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: Icon(_isNightMode ? Icons.light_mode : Icons.dark_mode),
          onPressed: _toggleNightMode,
          tooltip: _isNightMode ? 'الوضع النهاري' : 'الوضع الليلي',
        ),
        IconButton(
          icon: const Icon(Icons.bookmark),
          onPressed: _showBookmarksDialog,
          tooltip: 'الإشارات المرجعية',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'fullscreen',
              child: ListTile(
                leading: Icon(Icons.fullscreen),
                title: Text('ملء الشاشة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('إعدادات القراءة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'search',
              child: ListTile(
                leading: Icon(Icons.search),
                title: Text('البحث في النص'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
        ),
      );
    }

    return GestureDetector(
      onTap: _toggleControlsVisibility,
      child: Stack(
        children: [
          // قارئ PDF
          Container(
            color: _isNightMode ? Colors.black : Colors.white,
            child: SfPdfViewer.file(
              _book.pdfPath,
              controller: _pdfController,
              scrollDirection: _scrollDirection,
              pageLayoutMode: _layoutMode,
              enableDoubleTapZooming: true,
              enableTextSelection: true,
              canShowScrollHead: false,
              canShowScrollStatus: false,
              canShowPaginationDialog: false,
              onDocumentLoaded: _onDocumentLoaded,
              onPageChanged: _onPageChanged,
              onZoomLevelChanged: _onZoomLevelChanged,
            ),
          ),

          // شريط التحكم العلوي (في وضع ملء الشاشة)
          if (_isFullScreen && _isControlsVisible)
            _buildFullScreenTopBar(),

          // شريط التحكم السفلي (في وضع ملء الشاشة)
          if (_isFullScreen && _isControlsVisible)
            _buildFullScreenBottomBar(),

          // مؤشر الصفحة
          if (_isControlsVisible)
            _buildPageIndicator(),
        ],
      ),
    );
  }

  Widget _buildFullScreenTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          left: AppConstants.paddingMedium,
          right: AppConstants.paddingMedium,
          bottom: AppConstants.paddingMedium,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            Expanded(
              child: Text(
                _book.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            IconButton(
              icon: Icon(
                _isNightMode ? Icons.light_mode : Icons.dark_mode,
                color: Colors.white,
              ),
              onPressed: _toggleNightMode,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullScreenBottomBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: AppConstants.paddingMedium,
          right: AppConstants.paddingMedium,
          bottom: MediaQuery.of(context).padding.bottom + AppConstants.paddingMedium,
          top: AppConstants.paddingMedium,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.bookmark_add, color: Colors.white),
              onPressed: _addBookmark,
            ),
            IconButton(
              icon: const Icon(Icons.zoom_in, color: Colors.white),
              onPressed: _zoomIn,
            ),
            IconButton(
              icon: const Icon(Icons.zoom_out, color: Colors.white),
              onPressed: _zoomOut,
            ),
            IconButton(
              icon: const Icon(Icons.fullscreen_exit, color: Colors.white),
              onPressed: _toggleFullScreen,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Positioned(
      bottom: _isFullScreen ? 100 : 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
        child: Text(
          '$_currentPage / $_totalPages',
          style: const TextStyle(
            color: Colors.white,
            fontSize: AppConstants.fontSizeSmall,
            fontWeight: FontWeight.bold,
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return AnimatedContainer(
      duration: AppConstants.animationDurationShort,
      height: _isControlsVisible ? null : 0,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: _isNightMode ? Colors.grey[900] : Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              // زر الصفحة السابقة
              IconButton(
                icon: const Icon(Icons.navigate_before),
                onPressed: _currentPage > 1 ? _previousPage : null,
                color: _isNightMode ? Colors.white : Colors.black,
              ),
              
              // شريط تمرير الصفحات
              Expanded(
                child: Slider(
                  value: _currentPage.toDouble(),
                  min: 1,
                  max: _totalPages.toDouble(),
                  divisions: _totalPages > 1 ? _totalPages - 1 : 1,
                  activeColor: AppConstants.primaryColor,
                  inactiveColor: Colors.grey[300],
                  onChanged: (value) {
                    _goToPage(value.round());
                  },
                ),
              ),
              
              // زر الصفحة التالية
              IconButton(
                icon: const Icon(Icons.navigate_next),
                onPressed: _currentPage < _totalPages ? _nextPage : null,
                color: _isNightMode ? Colors.white : Colors.black,
              ),
              
              // زر إضافة إشارة مرجعية
              IconButton(
                icon: Icon(
                  _isBookmarked(_currentPage) 
                      ? Icons.bookmark 
                      : Icons.bookmark_border,
                  color: _isBookmarked(_currentPage) 
                      ? AppConstants.primaryColor 
                      : (_isNightMode ? Colors.white : Colors.black),
                ),
                onPressed: _toggleBookmark,
              ),
              
              // زر ملء الشاشة
              IconButton(
                icon: const Icon(Icons.fullscreen),
                onPressed: _toggleFullScreen,
                color: _isNightMode ? Colors.white : Colors.black,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
    });
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });
    
    // حفظ تقدم القراءة كل 5 صفحات
    if (_currentPage % 5 == 0) {
      _saveProgress();
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });
  }

  Future<void> _saveProgress() async {
    await _pdfService.saveReadingProgress(
      bookId: _book.id,
      currentPage: _currentPage,
      totalPages: _totalPages,
      additionalReadingTime: 1, // دقيقة واحدة لكل 5 صفحات
    );
  }

  void _toggleControlsVisibility() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
  }

  void _toggleNightMode() {
    setState(() {
      _isNightMode = !_isNightMode;
    });
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
    
    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pdfController.previousPage();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pdfController.nextPage();
    }
  }

  void _goToPage(int page) {
    _pdfController.jumpToPage(page);
  }

  void _zoomIn() {
    final newZoom = (_zoomLevel + 0.25).clamp(0.5, 3.0);
    _pdfController.zoomLevel = newZoom;
  }

  void _zoomOut() {
    final newZoom = (_zoomLevel - 0.25).clamp(0.5, 3.0);
    _pdfController.zoomLevel = newZoom;
  }

  bool _isBookmarked(int page) {
    return _bookmarks.any((bookmark) => bookmark.pageNumber == page);
  }

  void _toggleBookmark() {
    if (_isBookmarked(_currentPage)) {
      _removeBookmark(_currentPage);
    } else {
      _addBookmark();
    }
  }

  void _addBookmark() {
    _showAddBookmarkDialog();
  }

  void _showAddBookmarkDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة إشارة مرجعية - صفحة $_currentPage',
          style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: TextField(
          controller: controller,
          textDirection: TextDirection.rtl,
          style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
          decoration: const InputDecoration(
            hintText: 'ملاحظة (اختياري)',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              _saveBookmark(controller.text.trim());
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'حفظ',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  Future<void> _saveBookmark(String note) async {
    try {
      await _pdfService.addBookmark(
        bookId: _book.id,
        pageNumber: _currentPage,
        note: note.isEmpty ? 'صفحة $_currentPage' : note,
      );
      
      // تحديث قائمة الإشارات المرجعية
      final bookmarks = await _pdfService.getBookmarks(_book.id);
      setState(() {
        _bookmarks = bookmarks;
      });
      
      AppHelpers.showSuccessMessage(context, 'تم حفظ الإشارة المرجعية');
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في حفظ الإشارة المرجعية');
    }
  }

  Future<void> _removeBookmark(int page) async {
    try {
      await _pdfService.removeBookmark(
        bookId: _book.id,
        pageNumber: page,
      );
      
      // تحديث قائمة الإشارات المرجعية
      final bookmarks = await _pdfService.getBookmarks(_book.id);
      setState(() {
        _bookmarks = bookmarks;
      });
      
      AppHelpers.showSuccessMessage(context, 'تم حذف الإشارة المرجعية');
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في حذف الإشارة المرجعية');
    }
  }

  void _showBookmarksDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'الإشارات المرجعية',
          style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: _bookmarks.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد إشارات مرجعية',
                    style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
                  ),
                )
              : ListView.builder(
                  itemCount: _bookmarks.length,
                  itemBuilder: (context, index) {
                    final bookmark = _bookmarks[index];
                    return ListTile(
                      leading: const Icon(Icons.bookmark),
                      title: Text(
                        'صفحة ${bookmark.pageNumber}',
                        style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
                      ),
                      subtitle: bookmark.note.isNotEmpty
                          ? Text(
                              bookmark.note,
                              style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
                            )
                          : null,
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () {
                          _removeBookmark(bookmark.pageNumber);
                          Navigator.of(context).pop();
                        },
                      ),
                      onTap: () {
                        _goToPage(bookmark.pageNumber);
                        Navigator.of(context).pop();
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إغلاق',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'fullscreen':
        _toggleFullScreen();
        break;
      case 'settings':
        _showSettingsDialog();
        break;
      case 'search':
        _showSearchDialog();
        break;
    }
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'إعدادات القراءة',
          style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text(
                'اتجاه التمرير',
                style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
              ),
              trailing: DropdownButton<PdfScrollDirection>(
                value: _scrollDirection,
                items: const [
                  DropdownMenuItem(
                    value: PdfScrollDirection.vertical,
                    child: Text('عمودي'),
                  ),
                  DropdownMenuItem(
                    value: PdfScrollDirection.horizontal,
                    child: Text('أفقي'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _scrollDirection = value;
                    });
                    Navigator.of(context).pop();
                  }
                },
              ),
            ),
            ListTile(
              title: const Text(
                'تخطيط الصفحة',
                style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
              ),
              trailing: DropdownButton<PdfPageLayoutMode>(
                value: _layoutMode,
                items: const [
                  DropdownMenuItem(
                    value: PdfPageLayoutMode.single,
                    child: Text('صفحة واحدة'),
                  ),
                  DropdownMenuItem(
                    value: PdfPageLayoutMode.continuous,
                    child: Text('مستمر'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _layoutMode = value;
                    });
                    Navigator.of(context).pop();
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إغلاق',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'البحث في النص',
          style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: TextField(
          controller: controller,
          textDirection: TextDirection.rtl,
          style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
          decoration: const InputDecoration(
            hintText: 'أدخل النص للبحث عنه',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final query = controller.text.trim();
              if (query.isNotEmpty) {
                _pdfController.searchText(query);
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'بحث',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }
}
