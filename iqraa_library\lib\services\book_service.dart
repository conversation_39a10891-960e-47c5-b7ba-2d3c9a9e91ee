import 'dart:io';
import 'dart:math';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;
import '../models/book.dart';
import '../models/reading_progress.dart';
import 'storage_service.dart';

class BookService {
  static BookService? _instance;
  static BookService get instance => _instance ??= BookService._();
  BookService._();

  final StorageService _storage = StorageService.instance;
  final Uuid _uuid = const Uuid();

  // إضافة كتاب جديد
  Future<Book> addBook({
    required String title,
    required String author,
    required String description,
    required String category,
    required List<String> tags,
    required String pdfFilePath,
    String? coverImagePath,
  }) async {
    final bookId = _uuid.v4();
    
    // نسخ ملف PDF إلى مجلد الكتب
    final booksDir = await _storage.getBooksDirectory();
    final pdfFileName = '${bookId}_${path.basename(pdfFilePath)}';
    final newPdfPath = path.join(booksDir, pdfFileName);
    
    final originalFile = File(pdfFilePath);
    await originalFile.copy(newPdfPath);

    // نسخ صورة الغلاف إذا كانت متوفرة
    String finalCoverPath = '';
    if (coverImagePath != null && coverImagePath.isNotEmpty) {
      final coversDir = await _storage.getCoversDirectory();
      final coverFileName = '${bookId}_cover${path.extension(coverImagePath)}';
      finalCoverPath = path.join(coversDir, coverFileName);
      
      final coverFile = File(coverImagePath);
      await coverFile.copy(finalCoverPath);
    } else {
      // إنشاء غلاف افتراضي
      finalCoverPath = await _generateDefaultCover(bookId, title, author);
    }

    final book = Book(
      id: bookId,
      title: title,
      author: author,
      description: description,
      category: category,
      tags: tags,
      coverImagePath: finalCoverPath,
      pdfPath: newPdfPath,
      dateAdded: DateTime.now(),
    );

    await _storage.addBook(book);
    
    // إضافة التصنيف إلى قائمة التصنيفات إذا لم يكن موجوداً
    await _storage.addCategory(category);

    return book;
  }

  // الحصول على جميع الكتب
  Future<List<Book>> getAllBooks() async {
    return await _storage.getBooks();
  }

  // البحث في الكتب
  Future<List<Book>> searchBooks({
    String? query,
    String? category,
    List<String>? tags,
    ReadingStatus? status,
    bool? isFavorite,
  }) async {
    final allBooks = await getAllBooks();
    
    return allBooks.where((book) {
      // البحث النصي
      if (query != null && query.isNotEmpty) {
        final searchQuery = query.toLowerCase();
        final titleMatch = book.title.toLowerCase().contains(searchQuery);
        final authorMatch = book.author.toLowerCase().contains(searchQuery);
        final descriptionMatch = book.description.toLowerCase().contains(searchQuery);
        
        if (!titleMatch && !authorMatch && !descriptionMatch) {
          return false;
        }
      }

      // فلترة حسب التصنيف
      if (category != null && category.isNotEmpty && book.category != category) {
        return false;
      }

      // فلترة حسب التاغات
      if (tags != null && tags.isNotEmpty) {
        final hasMatchingTag = tags.any((tag) => book.tags.contains(tag));
        if (!hasMatchingTag) return false;
      }

      // فلترة حسب حالة القراءة
      if (status != null && book.readingStatus != status) {
        return false;
      }

      // فلترة حسب المفضلة
      if (isFavorite != null && book.isFavorite != isFavorite) {
        return false;
      }

      return true;
    }).toList();
  }

  // الحصول على الكتب المضافة حديثاً
  Future<List<Book>> getRecentBooks({int limit = 10}) async {
    final allBooks = await getAllBooks();
    allBooks.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));
    return allBooks.take(limit).toList();
  }

  // الحصول على الكتب المفضلة
  Future<List<Book>> getFavoriteBooks() async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.isFavorite).toList();
  }

  // الحصول على الكتب قيد القراءة
  Future<List<Book>> getCurrentlyReadingBooks() async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.readingStatus == ReadingStatus.reading).toList();
  }

  // الحصول على الكتب المكتملة
  Future<List<Book>> getCompletedBooks() async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.readingStatus == ReadingStatus.completed).toList();
  }

  // تحديث كتاب
  Future<void> updateBook(Book book) async {
    await _storage.updateBook(book);
  }

  // تبديل حالة المفضلة
  Future<void> toggleFavorite(String bookId) async {
    final book = await _storage.getBookById(bookId);
    if (book != null) {
      final updatedBook = book.copyWith(isFavorite: !book.isFavorite);
      await _storage.updateBook(updatedBook);
    }
  }

  // تحديث حالة القراءة
  Future<void> updateReadingStatus(String bookId, ReadingStatus status) async {
    final book = await _storage.getBookById(bookId);
    if (book != null) {
      final updatedBook = book.copyWith(readingStatus: status);
      await _storage.updateBook(updatedBook);
    }
  }

  // تحديث تقدم القراءة
  Future<void> updateReadingProgress(String bookId, int currentPage, int totalPages) async {
    final book = await _storage.getBookById(bookId);
    if (book != null) {
      final updatedBook = book.copyWith(
        currentPage: currentPage,
        totalPages: totalPages,
      );
      await _storage.updateBook(updatedBook);

      // تحديث تقدم القراءة في الخدمة المنفصلة
      final progress = ReadingProgress(
        bookId: bookId,
        currentPage: currentPage,
        totalPages: totalPages,
        lastReadDate: DateTime.now(),
      );
      await _storage.saveReadingProgress(progress);

      // تحديث حالة القراءة تلقائياً
      if (currentPage >= totalPages && book.readingStatus != ReadingStatus.completed) {
        await updateReadingStatus(bookId, ReadingStatus.completed);
      } else if (currentPage > 0 && book.readingStatus == ReadingStatus.wantToRead) {
        await updateReadingStatus(bookId, ReadingStatus.reading);
      }
    }
  }

  // حذف كتاب
  Future<void> deleteBook(String bookId) async {
    final book = await _storage.getBookById(bookId);
    if (book != null) {
      // حذف ملفات الكتاب
      try {
        final pdfFile = File(book.pdfPath);
        if (await pdfFile.exists()) {
          await pdfFile.delete();
        }

        final coverFile = File(book.coverImagePath);
        if (await coverFile.exists()) {
          await coverFile.delete();
        }
      } catch (e) {
        // تجاهل أخطاء حذف الملفات
      }

      // حذف الكتاب من قاعدة البيانات
      await _storage.deleteBook(bookId);
    }
  }

  // الحصول على إحصائيات القراءة
  Future<ReadingStatistics> getReadingStatistics() async {
    final allBooks = await getAllBooks();
    final allProgress = await _storage.getAllReadingProgress();

    final totalBooks = allBooks.length;
    final completedBooks = allBooks.where((book) => book.readingStatus == ReadingStatus.completed).length;
    final currentlyReading = allBooks.where((book) => book.readingStatus == ReadingStatus.reading).length;
    final favoriteBooks = allBooks.where((book) => book.isFavorite).length;

    final totalReadingTime = allProgress.fold<int>(
      0,
      (sum, progress) => sum + progress.readingTimeMinutes,
    );

    final totalPagesRead = allBooks.fold<int>(
      0,
      (sum, book) => sum + book.currentPage,
    );

    // حساب متوسط التقييم
    final ratedBooks = allBooks.where((book) => book.rating > 0);
    final averageRating = ratedBooks.isEmpty 
        ? 0.0 
        : ratedBooks.fold<double>(0, (sum, book) => sum + book.rating) / ratedBooks.length;

    return ReadingStatistics(
      totalBooks: totalBooks,
      completedBooks: completedBooks,
      currentlyReading: currentlyReading,
      favoriteBooks: favoriteBooks,
      totalReadingTimeMinutes: totalReadingTime,
      totalPagesRead: totalPagesRead,
      averageRating: averageRating,
    );
  }

  // إنشاء غلاف افتراضي
  Future<String> _generateDefaultCover(String bookId, String title, String author) async {
    // هنا يمكن إنشاء صورة غلاف افتراضية
    // للبساطة، سنعيد مسار فارغ ونستخدم أيقونة افتراضية في الواجهة
    return '';
  }

  // الحصول على الكتب حسب التصنيف
  Future<List<Book>> getBooksByCategory(String category) async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.category == category).toList();
  }

  // الحصول على التصنيفات مع عدد الكتب
  Future<Map<String, int>> getCategoriesWithCount() async {
    final allBooks = await getAllBooks();
    final categoriesCount = <String, int>{};

    for (final book in allBooks) {
      categoriesCount[book.category] = (categoriesCount[book.category] ?? 0) + 1;
    }

    return categoriesCount;
  }

  // إنشاء كتب تجريبية
  Future<void> createSampleBooks() async {
    final sampleBooks = [
      {
        'title': 'رحلة في عالم البرمجة',
        'author': 'أحمد محمد',
        'description': 'كتاب شامل يتناول أساسيات البرمجة والتطوير',
        'category': 'تكنولوجيا',
        'tags': ['برمجة', 'تطوير', 'تعليم'],
      },
      {
        'title': 'فن الإدارة الحديثة',
        'author': 'فاطمة أحمد',
        'description': 'دليل شامل لتطوير مهارات الإدارة والقيادة',
        'category': 'تنمية بشرية',
        'tags': ['إدارة', 'قيادة', 'تطوير'],
      },
      {
        'title': 'تاريخ الحضارة الإسلامية',
        'author': 'محمد علي',
        'description': 'استعراض شامل لتاريخ الحضارة الإسلامية وإنجازاتها',
        'category': 'تاريخ',
        'tags': ['تاريخ', 'حضارة', 'إسلام'],
      },
    ];

    for (final bookData in sampleBooks) {
      // إنشاء ملف PDF وهمي للعرض التوضيحي
      final book = Book(
        id: _uuid.v4(),
        title: bookData['title'] as String,
        author: bookData['author'] as String,
        description: bookData['description'] as String,
        category: bookData['category'] as String,
        tags: bookData['tags'] as List<String>,
        coverImagePath: '',
        pdfPath: '',
        dateAdded: DateTime.now().subtract(Duration(days: Random().nextInt(30))),
        totalPages: 100 + Random().nextInt(400),
        currentPage: Random().nextInt(50),
      );

      await _storage.addBook(book);
    }
  }
}

class ReadingStatistics {
  final int totalBooks;
  final int completedBooks;
  final int currentlyReading;
  final int favoriteBooks;
  final int totalReadingTimeMinutes;
  final int totalPagesRead;
  final double averageRating;

  ReadingStatistics({
    required this.totalBooks,
    required this.completedBooks,
    required this.currentlyReading,
    required this.favoriteBooks,
    required this.totalReadingTimeMinutes,
    required this.totalPagesRead,
    required this.averageRating,
  });

  double get completionPercentage {
    if (totalBooks == 0) return 0.0;
    return (completedBooks / totalBooks) * 100;
  }

  String get totalReadingTimeFormatted {
    final hours = totalReadingTimeMinutes ~/ 60;
    final minutes = totalReadingTimeMinutes % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
}
