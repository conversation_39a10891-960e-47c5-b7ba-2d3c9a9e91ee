import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/book.dart';
import '../services/book_service.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/book_card.dart';
import '../widgets/category_chip.dart';
import '../widgets/reading_progress_indicator.dart';
import 'search_screen.dart';
import 'book_details_screen.dart';
import 'add_book_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final BookService _bookService = BookService.instance;
  List<Book> _recentBooks = [];
  List<Book> _currentlyReading = [];
  List<Book> _favoriteBooks = [];
  Map<String, int> _categoriesCount = {};
  ReadingStatistics? _statistics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final recentBooks = await _bookService.getRecentBooks(limit: 5);
      final currentlyReading = await _bookService.getCurrentlyReadingBooks();
      final favoriteBooks = await _bookService.getFavoriteBooks();
      final categoriesCount = await _bookService.getCategoriesWithCount();
      final statistics = await _bookService.getReadingStatistics();

      setState(() {
        _recentBooks = recentBooks;
        _currentlyReading = currentlyReading;
        _favoriteBooks = favoriteBooks.take(3).toList();
        _categoriesCount = categoriesCount;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        AppHelpers.showErrorMessage(context, 'حدث خطأ في تحميل البيانات');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        AppConstants.appName,
        style: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => AppHelpers.navigateTo(context, const SearchScreen()),
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // TODO: تنفيذ الإشعارات
          },
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
      ),
    );
  }

  Widget _buildBody() {
    return RefreshIndicator(
      onRefresh: _loadData,
      color: AppConstants.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildStatisticsSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildCurrentlyReadingSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildCategoriesSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildRecentBooksSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildFavoriteBooksSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'صباح الخير';
    } else if (hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: AppHelpers.createGradient(
          startColor: AppConstants.primaryColor,
          endColor: AppConstants.primaryLightColor,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            greeting,
            style: const TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          const Text(
            'مرحباً بك في مكتبة إقرأ',
            style: TextStyle(
              color: Colors.white70,
              fontSize: AppConstants.fontSizeMedium,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            children: [
              Expanded(
                child: _buildQuickStat(
                  'الكتب',
                  _statistics?.totalBooks.toString() ?? '0',
                  Icons.book,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: _buildQuickStat(
                  'مقروء',
                  _statistics?.completedBooks.toString() ?? '0',
                  Icons.check_circle,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: _buildQuickStat(
                  'جارٍ القراءة',
                  _statistics?.currentlyReading.toString() ?? '0',
                  Icons.auto_stories,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: AppConstants.iconSizeLarge,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: AppConstants.fontSizeSmall,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection() {
    if (_statistics == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('إحصائيات القراءة'),
        const SizedBox(height: AppConstants.paddingMedium),
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            boxShadow: AppHelpers.createShadow(),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'وقت القراءة',
                      _statistics!.totalReadingTimeFormatted,
                      Icons.access_time,
                      AppConstants.infoColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: _buildStatItem(
                      'الصفحات المقروءة',
                      _statistics!.totalPagesRead.toString(),
                      Icons.description,
                      AppConstants.successColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'المفضلة',
                      _statistics!.favoriteBooks.toString(),
                      Icons.favorite,
                      AppConstants.errorColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: _buildStatItem(
                      'متوسط التقييم',
                      AppHelpers.formatRating(_statistics!.averageRating),
                      Icons.star,
                      AppConstants.warningColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppConstants.iconSizeLarge),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: color.withOpacity(0.8),
              fontSize: AppConstants.fontSizeSmall,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentlyReadingSection() {
    if (_currentlyReading.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('متابعة القراءة'),
        const SizedBox(height: AppConstants.paddingMedium),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _currentlyReading.length,
            itemBuilder: (context, index) {
              final book = _currentlyReading[index];
              return Container(
                width: 300,
                margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
                child: _buildCurrentlyReadingCard(book),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentlyReadingCard(Book book) {
    return GestureDetector(
      onTap: () => AppHelpers.navigateTo(
        context,
        BookDetailsScreen(book: book),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: AppHelpers.createShadow(),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 80,
              decoration: BoxDecoration(
                color: AppHelpers.getCategoryColor(book.category).withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Icon(
                AppHelpers.getCategoryIcon(book.category),
                color: AppHelpers.getCategoryColor(book.category),
                size: AppConstants.iconSizeLarge,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    book.title,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.bold,
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    book.author,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: Colors.grey[600],
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  ReadingProgressIndicator(
                    progress: book.readingProgress,
                    height: 6,
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    '${AppHelpers.formatPercentage(book.readingProgress * 100)} مكتمل',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: Colors.grey[600],
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    if (_categoriesCount.isEmpty) return const SizedBox.shrink();

    final categories = _categoriesCount.entries.take(6).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('التصنيفات'),
        const SizedBox(height: AppConstants.paddingMedium),
        Wrap(
          spacing: AppConstants.paddingSmall,
          runSpacing: AppConstants.paddingSmall,
          children: categories.map((entry) {
            return CategoryChip(
              category: entry.key,
              count: entry.value,
              onTap: () {
                // TODO: التنقل إلى صفحة التصنيف
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRecentBooksSection() {
    if (_recentBooks.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSectionTitle('أحدث الكتب'),
            TextButton(
              onPressed: () {
                // TODO: عرض جميع الكتب
              },
              child: const Text(
                'عرض الكل',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        SizedBox(
          height: AppConstants.cardHeight + 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _recentBooks.length,
            itemBuilder: (context, index) {
              final book = _recentBooks[index];
              return Container(
                width: AppConstants.cardWidth,
                margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
                child: BookCard(
                  book: book,
                  onTap: () => AppHelpers.navigateTo(
                    context,
                    BookDetailsScreen(book: book),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFavoriteBooksSection() {
    if (_favoriteBooks.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSectionTitle('المفضلة'),
            TextButton(
              onPressed: () {
                // TODO: عرض جميع المفضلة
              },
              child: const Text(
                'عرض الكل',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        SizedBox(
          height: AppConstants.cardHeight + 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _favoriteBooks.length,
            itemBuilder: (context, index) {
              final book = _favoriteBooks[index];
              return Container(
                width: AppConstants.cardWidth,
                margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
                child: BookCard(
                  book: book,
                  onTap: () => AppHelpers.navigateTo(
                    context,
                    BookDetailsScreen(book: book),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeXLarge,
        fontWeight: FontWeight.bold,
        color: AppConstants.primaryDarkColor,
        fontFamily: AppConstants.fontFamilyPrimary,
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () async {
        final result = await AppHelpers.navigateTo(
          context,
          const AddBookScreen(),
        );
        if (result == true) {
          _loadData();
        }
      },
      backgroundColor: AppConstants.primaryColor,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }
}
