import 'package:flutter/material.dart';
import '../models/book.dart';
import '../services/book_service.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/reading_progress_indicator.dart';
import 'pdf_reader_screen.dart';

class BookDetailsScreen extends StatefulWidget {
  final Book book;

  const BookDetailsScreen({
    super.key,
    required this.book,
  });

  @override
  State<BookDetailsScreen> createState() => _BookDetailsScreenState();
}

class _BookDetailsScreenState extends State<BookDetailsScreen> {
  final BookService _bookService = BookService.instance;
  late Book _book;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _book = widget.book;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: _buildBody(),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: _buildCoverSection(),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _book.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _book.isFavorite ? AppConstants.errorColor : Colors.white,
          ),
          onPressed: _toggleFavorite,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('مشاركة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCoverSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppHelpers.createGradient(
          startColor: AppHelpers.getCategoryColor(_book.category).withOpacity(0.8),
          endColor: AppHelpers.getCategoryColor(_book.category).withOpacity(0.4),
        ),
      ),
      child: Stack(
        children: [
          // خلفية متدرجة
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ),
          
          // محتوى الغلاف
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // صورة الغلاف
                      Container(
                        width: 120,
                        height: 160,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                          boxShadow: AppHelpers.createShadow(
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ),
                        child: _book.coverImagePath.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                                child: Image.asset(
                                  _book.coverImagePath,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => _buildDefaultCover(),
                                ),
                              )
                            : _buildDefaultCover(),
                      ),
                      
                      const SizedBox(width: AppConstants.paddingLarge),
                      
                      // معلومات الكتاب
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _book.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: AppConstants.fontSizeXLarge,
                                fontWeight: FontWeight.bold,
                                fontFamily: AppConstants.fontFamilyPrimary,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              _book.author,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: AppConstants.fontSizeMedium,
                                fontFamily: AppConstants.fontFamilyPrimary,
                              ),
                            ),
                            const SizedBox(height: AppConstants.paddingMedium),
                            _buildRatingSection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultCover() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppHelpers.createGradient(
          startColor: AppHelpers.getCategoryColor(_book.category),
          endColor: AppHelpers.getCategoryColor(_book.category).withOpacity(0.7),
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Center(
        child: Icon(
          AppHelpers.getCategoryIcon(_book.category),
          size: 60,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildRatingSection() {
    return Row(
      children: [
        ...List.generate(5, (index) {
          return Icon(
            index < _book.rating ? Icons.star : Icons.star_border,
            color: AppConstants.warningColor,
            size: 20,
          );
        }),
        const SizedBox(width: AppConstants.paddingSmall),
        Text(
          AppHelpers.formatRating(_book.rating),
          style: const TextStyle(
            color: Colors.white,
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickInfoSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildProgressSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildDescriptionSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildDetailsSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildTagsSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildActionsSection(),
          const SizedBox(height: 100), // مساحة للشريط السفلي
        ],
      ),
    );
  }

  Widget _buildQuickInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickInfoItem(
              'التصنيف',
              _book.category,
              AppHelpers.getCategoryIcon(_book.category),
              AppHelpers.getCategoryColor(_book.category),
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildQuickInfoItem(
              'الحالة',
              _book.readingStatus.displayName,
              _getStatusIcon(_book.readingStatus),
              _getStatusColor(_book.readingStatus),
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildQuickInfoItem(
              'الصفحات',
              _book.totalPages > 0 ? _book.totalPages.toString() : 'غير محدد',
              Icons.description,
              AppConstants.infoColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInfoItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: AppConstants.iconSizeLarge),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          value,
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
            color: color,
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: Colors.grey[600],
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    if (_book.readingStatus != ReadingStatus.reading && _book.currentPage == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'تقدم القراءة',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
              Text(
                '${_book.currentPage} من ${_book.totalPages}',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: Colors.grey[600],
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          AnimatedReadingProgress(
            progress: _book.readingProgress,
            height: 10,
            showPercentage: true,
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    if (_book.description.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الوصف',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            _book.description,
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.grey[700],
              height: 1.6,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تفاصيل إضافية',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildDetailRow('تاريخ الإضافة', AppHelpers.formatDate(_book.dateAdded)),
          _buildDetailRow('التقييم', '${AppHelpers.formatRating(_book.rating)} من 5'),
          if (_book.totalPages > 0)
            _buildDetailRow('عدد الصفحات', AppHelpers.formatPageCount(_book.totalPages)),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.grey[600],
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    if (_book.tags.isEmpty) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'العلامات',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children: _book.tags.map((tag) => _buildTagChip(tag)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTagChip(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        tag,
        style: const TextStyle(
          color: AppConstants.primaryColor,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إجراءات',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'تغيير الحالة',
                  Icons.swap_horiz,
                  AppConstants.infoColor,
                  _showStatusDialog,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: _buildActionButton(
                  'تقييم',
                  Icons.star,
                  AppConstants.warningColor,
                  _showRatingDialog,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: color),
      label: Text(
        label,
        style: TextStyle(
          color: color,
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: color),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
        padding: const EdgeInsets.symmetric(
          vertical: AppConstants.paddingMedium,
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: ElevatedButton.icon(
          onPressed: _openReader,
          icon: Icon(_getReadButtonIcon()),
          label: Text(
            _getReadButtonText(),
            style: const TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              vertical: AppConstants.paddingLarge,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getReadButtonIcon() {
    switch (_book.readingStatus) {
      case ReadingStatus.reading:
        return Icons.play_arrow;
      case ReadingStatus.completed:
        return Icons.replay;
      case ReadingStatus.wantToRead:
        return Icons.menu_book;
    }
  }

  String _getReadButtonText() {
    switch (_book.readingStatus) {
      case ReadingStatus.reading:
        return 'متابعة القراءة';
      case ReadingStatus.completed:
        return 'قراءة مرة أخرى';
      case ReadingStatus.wantToRead:
        return 'بدء القراءة';
    }
  }

  IconData _getStatusIcon(ReadingStatus status) {
    switch (status) {
      case ReadingStatus.reading:
        return Icons.auto_stories;
      case ReadingStatus.completed:
        return Icons.check_circle;
      case ReadingStatus.wantToRead:
        return Icons.bookmark_border;
    }
  }

  Color _getStatusColor(ReadingStatus status) {
    switch (status) {
      case ReadingStatus.reading:
        return AppConstants.infoColor;
      case ReadingStatus.completed:
        return AppConstants.successColor;
      case ReadingStatus.wantToRead:
        return AppConstants.warningColor;
    }
  }

  Future<void> _toggleFavorite() async {
    setState(() => _isLoading = true);
    
    try {
      await _bookService.toggleFavorite(_book.id);
      setState(() {
        _book = _book.copyWith(isFavorite: !_book.isFavorite);
        _isLoading = false;
      });
      
      AppHelpers.showSuccessMessage(
        context,
        _book.isFavorite ? 'تم إضافة الكتاب للمفضلة' : 'تم إزالة الكتاب من المفضلة',
      );
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في تحديث المفضلة');
    }
  }

  void _openReader() {
    AppHelpers.navigateTo(
      context,
      PdfReaderScreen(book: _book),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        // TODO: فتح شاشة التعديل
        break;
      case 'share':
        // TODO: مشاركة الكتاب
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  void _showStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تغيير حالة القراءة',
          style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ReadingStatus.values.map((status) {
            return ListTile(
              leading: Icon(_getStatusIcon(status)),
              title: Text(
                status.displayName,
                style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _updateStatus(status);
              },
            );
          }).toList(),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  void _showRatingDialog() {
    double tempRating = _book.rating;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text(
            'تقييم الكتاب',
            style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () => setState(() => tempRating = index + 1.0),
                    child: Icon(
                      index < tempRating ? Icons.star : Icons.star_border,
                      color: AppConstants.warningColor,
                      size: 40,
                    ),
                  );
                }),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Text(
                'التقييم: ${AppHelpers.formatRating(tempRating)}',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _updateRating(tempRating);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'حفظ',
                style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
              ),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
        ),
      ),
    );
  }

  void _showDeleteDialog() async {
    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف الكتاب',
      message: 'هل أنت متأكد من حذف هذا الكتاب؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      _deleteBook();
    }
  }

  Future<void> _updateStatus(ReadingStatus status) async {
    setState(() => _isLoading = true);
    
    try {
      await _bookService.updateReadingStatus(_book.id, status);
      setState(() {
        _book = _book.copyWith(readingStatus: status);
        _isLoading = false;
      });
      
      AppHelpers.showSuccessMessage(context, 'تم تحديث حالة القراءة');
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في تحديث الحالة');
    }
  }

  Future<void> _updateRating(double rating) async {
    setState(() => _isLoading = true);
    
    try {
      final updatedBook = _book.copyWith(rating: rating);
      await _bookService.updateBook(updatedBook);
      setState(() {
        _book = updatedBook;
        _isLoading = false;
      });
      
      AppHelpers.showSuccessMessage(context, 'تم حفظ التقييم');
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في حفظ التقييم');
    }
  }

  Future<void> _deleteBook() async {
    AppHelpers.showLoadingDialog(context, message: 'جارٍ حذف الكتاب...');
    
    try {
      await _bookService.deleteBook(_book.id);
      AppHelpers.hideLoadingDialog(context);
      AppHelpers.showSuccessMessage(context, 'تم حذف الكتاب بنجاح');
      Navigator.of(context).pop(true); // العودة مع إشارة التحديث
    } catch (e) {
      AppHelpers.hideLoadingDialog(context);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في حذف الكتاب');
    }
  }
}
