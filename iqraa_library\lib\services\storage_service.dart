import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';
import '../models/user_preferences.dart';

class StorageService {
  static const String _booksKey = 'books';
  static const String _progressKey = 'reading_progress';
  static const String _preferencesKey = 'user_preferences';
  static const String _categoriesKey = 'categories';

  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  StorageService._();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // إدارة الكتب
  Future<List<Book>> getBooks() async {
    final booksJson = _prefs?.getString(_booksKey);
    if (booksJson == null) return [];
    
    final List<dynamic> booksList = jsonDecode(booksJson);
    return booksList.map((json) => Book.fromJson(json)).toList();
  }

  Future<void> saveBooks(List<Book> books) async {
    final booksJson = jsonEncode(books.map((book) => book.toJson()).toList());
    await _prefs?.setString(_booksKey, booksJson);
  }

  Future<void> addBook(Book book) async {
    final books = await getBooks();
    books.add(book);
    await saveBooks(books);
  }

  Future<void> updateBook(Book book) async {
    final books = await getBooks();
    final index = books.indexWhere((b) => b.id == book.id);
    if (index != -1) {
      books[index] = book;
      await saveBooks(books);
    }
  }

  Future<void> deleteBook(String bookId) async {
    final books = await getBooks();
    books.removeWhere((book) => book.id == bookId);
    await saveBooks(books);
    
    // حذف تقدم القراءة أيضاً
    await deleteReadingProgress(bookId);
  }

  Future<Book?> getBookById(String id) async {
    final books = await getBooks();
    try {
      return books.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  // إدارة تقدم القراءة
  Future<List<ReadingProgress>> getAllReadingProgress() async {
    final progressJson = _prefs?.getString(_progressKey);
    if (progressJson == null) return [];
    
    final List<dynamic> progressList = jsonDecode(progressJson);
    return progressList.map((json) => ReadingProgress.fromJson(json)).toList();
  }

  Future<ReadingProgress?> getReadingProgress(String bookId) async {
    final progressList = await getAllReadingProgress();
    try {
      return progressList.firstWhere((progress) => progress.bookId == bookId);
    } catch (e) {
      return null;
    }
  }

  Future<void> saveReadingProgress(ReadingProgress progress) async {
    final progressList = await getAllReadingProgress();
    final index = progressList.indexWhere((p) => p.bookId == progress.bookId);
    
    if (index != -1) {
      progressList[index] = progress;
    } else {
      progressList.add(progress);
    }
    
    final progressJson = jsonEncode(progressList.map((p) => p.toJson()).toList());
    await _prefs?.setString(_progressKey, progressJson);
  }

  Future<void> deleteReadingProgress(String bookId) async {
    final progressList = await getAllReadingProgress();
    progressList.removeWhere((progress) => progress.bookId == bookId);
    
    final progressJson = jsonEncode(progressList.map((p) => p.toJson()).toList());
    await _prefs?.setString(_progressKey, progressJson);
  }

  // إدارة تفضيلات المستخدم
  Future<UserPreferences> getUserPreferences() async {
    final prefsJson = _prefs?.getString(_preferencesKey);
    if (prefsJson == null) return UserPreferences();
    
    return UserPreferences.fromJson(jsonDecode(prefsJson));
  }

  Future<void> saveUserPreferences(UserPreferences preferences) async {
    final prefsJson = jsonEncode(preferences.toJson());
    await _prefs?.setString(_preferencesKey, prefsJson);
  }

  // إدارة التصنيفات
  Future<List<String>> getCategories() async {
    final categoriesJson = _prefs?.getString(_categoriesKey);
    if (categoriesJson == null) {
      return _getDefaultCategories();
    }
    
    final List<dynamic> categoriesList = jsonDecode(categoriesJson);
    return categoriesList.cast<String>();
  }

  Future<void> saveCategories(List<String> categories) async {
    final categoriesJson = jsonEncode(categories);
    await _prefs?.setString(_categoriesKey, categoriesJson);
  }

  Future<void> addCategory(String category) async {
    final categories = await getCategories();
    if (!categories.contains(category)) {
      categories.add(category);
      await saveCategories(categories);
    }
  }

  List<String> _getDefaultCategories() {
    return [
      'ديني',
      'تنمية بشرية',
      'روايات',
      'تاريخ',
      'علوم',
      'فلسفة',
      'أدب',
      'شعر',
      'سيرة ذاتية',
      'تكنولوجيا',
      'طب',
      'قانون',
      'اقتصاد',
      'سياسة',
      'تعليم',
      'عام',
    ];
  }

  // إدارة الملفات
  Future<String> getApplicationDocumentsPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<String> getBooksDirectory() async {
    final appDir = await getApplicationDocumentsPath();
    final booksDir = Directory('$appDir/books');
    if (!await booksDir.exists()) {
      await booksDir.create(recursive: true);
    }
    return booksDir.path;
  }

  Future<String> getCoversDirectory() async {
    final appDir = await getApplicationDocumentsPath();
    final coversDir = Directory('$appDir/covers');
    if (!await coversDir.exists()) {
      await coversDir.create(recursive: true);
    }
    return coversDir.path;
  }

  // تنظيف البيانات
  Future<void> clearAllData() async {
    await _prefs?.clear();
  }

  Future<void> exportData() async {
    // تصدير البيانات كملف JSON
    final books = await getBooks();
    final progress = await getAllReadingProgress();
    final preferences = await getUserPreferences();
    final categories = await getCategories();

    final exportData = {
      'books': books.map((book) => book.toJson()).toList(),
      'progress': progress.map((p) => p.toJson()).toList(),
      'preferences': preferences.toJson(),
      'categories': categories,
      'exportDate': DateTime.now().toIso8601String(),
    };

    final appDir = await getApplicationDocumentsPath();
    final exportFile = File('$appDir/iqraa_backup.json');
    await exportFile.writeAsString(jsonEncode(exportData));
  }

  Future<bool> importData(String filePath) async {
    try {
      final file = File(filePath);
      final content = await file.readAsString();
      final data = jsonDecode(content);

      // استيراد الكتب
      if (data['books'] != null) {
        final books = (data['books'] as List)
            .map((json) => Book.fromJson(json))
            .toList();
        await saveBooks(books);
      }

      // استيراد تقدم القراءة
      if (data['progress'] != null) {
        final progressList = (data['progress'] as List)
            .map((json) => ReadingProgress.fromJson(json))
            .toList();
        
        for (final progress in progressList) {
          await saveReadingProgress(progress);
        }
      }

      // استيراد التفضيلات
      if (data['preferences'] != null) {
        final preferences = UserPreferences.fromJson(data['preferences']);
        await saveUserPreferences(preferences);
      }

      // استيراد التصنيفات
      if (data['categories'] != null) {
        final categories = (data['categories'] as List).cast<String>();
        await saveCategories(categories);
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}
