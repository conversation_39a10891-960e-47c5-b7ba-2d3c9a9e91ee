import 'package:flutter/material.dart';
import '../models/book.dart';
import '../services/book_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/book_card.dart';
import '../widgets/category_chip.dart';
import 'book_details_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final BookService _bookService = BookService.instance;
  final StorageService _storage = StorageService.instance;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  List<Book> _allBooks = [];
  List<Book> _filteredBooks = [];
  List<String> _categories = [];
  Map<String, int> _categoriesCount = {};
  String _selectedCategory = '';
  ReadingStatus? _selectedStatus;
  bool? _selectedFavorite;
  bool _isLoading = true;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final books = await _bookService.getAllBooks();
      final categories = await _storage.getCategories();
      final categoriesCount = await _bookService.getCategoriesWithCount();

      setState(() {
        _allBooks = books;
        _filteredBooks = books;
        _categories = categories;
        _categoriesCount = categoriesCount;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        AppHelpers.showErrorMessage(context, 'حدث خطأ في تحميل البيانات');
      }
    }
  }

  void _onSearchChanged() {
    _performSearch();
  }

  Future<void> _performSearch() async {
    setState(() => _isSearching = true);

    try {
      final filteredBooks = await _bookService.searchBooks(
        query: _searchController.text,
        category: _selectedCategory.isEmpty ? null : _selectedCategory,
        status: _selectedStatus,
        isFavorite: _selectedFavorite,
      );

      setState(() {
        _filteredBooks = filteredBooks;
        _isSearching = false;
      });
    } catch (e) {
      setState(() => _isSearching = false);
      if (mounted) {
        AppHelpers.showErrorMessage(context, 'حدث خطأ في البحث');
      }
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _selectedCategory = '';
      _selectedStatus = null;
      _selectedFavorite = null;
      _filteredBooks = _allBooks;
    });
  }

  void _onCategorySelected(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _performSearch();
  }

  void _onStatusSelected(ReadingStatus? status) {
    setState(() {
      _selectedStatus = status;
    });
    _performSearch();
  }

  void _onFavoriteSelected(bool? favorite) {
    setState(() {
      _selectedFavorite = favorite;
    });
    _performSearch();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'البحث',
        style: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (_searchController.text.isNotEmpty || 
            _selectedCategory.isNotEmpty || 
            _selectedStatus != null || 
            _selectedFavorite != null)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearSearch,
            tooltip: 'مسح البحث',
          ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'فلترة',
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildSearchSection(),
        _buildFiltersSection(),
        Expanded(child: _buildResultsSection()),
      ],
    );
  }

  Widget _buildSearchSection() {
    return Container(
      color: AppConstants.primaryColor,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: AppHelpers.createShadow(),
        ),
        child: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          textDirection: TextDirection.rtl,
          style: const TextStyle(
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
          decoration: InputDecoration(
            hintText: 'ابحث عن كتاب، مؤلف، أو وصف...',
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
            prefixIcon: _isSearching
                ? const Padding(
                    padding: EdgeInsets.all(AppConstants.paddingMedium),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  )
                : const Icon(
                    Icons.search,
                    color: AppConstants.primaryColor,
                  ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Colors.grey,
                    ),
                    onPressed: () => _searchController.clear(),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    final hasActiveFilters = _selectedCategory.isNotEmpty || 
        _selectedStatus != null || 
        _selectedFavorite != null;

    if (!hasActiveFilters && _categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_categories.isNotEmpty) ...[
            const Text(
              'التصنيفات',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            CategoryList(
              categories: _categories,
              categoriesCount: _categoriesCount,
              selectedCategory: _selectedCategory.isEmpty ? null : _selectedCategory,
              onCategorySelected: _onCategorySelected,
              scrollDirection: Axis.horizontal,
            ),
          ],
          
          if (hasActiveFilters) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            const Text(
              'الفلاتر النشطة',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Wrap(
              spacing: AppConstants.paddingSmall,
              runSpacing: AppConstants.paddingSmall,
              children: [
                if (_selectedStatus != null)
                  _buildActiveFilterChip(
                    _selectedStatus!.displayName,
                    () => _onStatusSelected(null),
                  ),
                if (_selectedFavorite != null)
                  _buildActiveFilterChip(
                    _selectedFavorite! ? 'المفضلة' : 'غير مفضلة',
                    () => _onFavoriteSelected(null),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveFilterChip(String label, VoidCallback onRemove) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppConstants.primaryColor,
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          GestureDetector(
            onTap: onRemove,
            child: const Icon(
              Icons.close,
              color: AppConstants.primaryColor,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_filteredBooks.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildResultsHeader(),
        Expanded(child: _buildBooksList()),
      ],
    );
  }

  Widget _buildResultsHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'النتائج (${_filteredBooks.length})',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortDialog,
            tooltip: 'ترتيب',
          ),
        ],
      ),
    );
  }

  Widget _buildBooksList() {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: AppConstants.paddingMedium,
        mainAxisSpacing: AppConstants.paddingMedium,
      ),
      itemCount: _filteredBooks.length,
      itemBuilder: (context, index) {
        final book = _filteredBooks[index];
        return BookCard(
          book: book,
          onTap: () => AppHelpers.navigateTo(
            context,
            BookDetailsScreen(book: book),
          ),
          onFavoriteToggle: () => _toggleFavorite(book),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              _searchController.text.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'ابدأ البحث عن الكتب',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              _searchController.text.isNotEmpty
                  ? 'جرب البحث بكلمات مختلفة أو قم بتعديل الفلاتر'
                  : 'استخدم شريط البحث أعلاه للعثور على الكتب',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: Colors.grey[500],
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            if (_searchController.text.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingLarge),
              ElevatedButton(
                onPressed: _clearSearch,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                  ),
                ),
                child: const Text(
                  'مسح البحث',
                  style: TextStyle(
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _toggleFavorite(Book book) async {
    try {
      await _bookService.toggleFavorite(book.id);
      _loadData(); // إعادة تحميل البيانات
      AppHelpers.showSuccessMessage(
        context,
        book.isFavorite ? 'تم إزالة الكتاب من المفضلة' : 'تم إضافة الكتاب للمفضلة',
      );
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في تحديث المفضلة');
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _FilterBottomSheet(
        selectedStatus: _selectedStatus,
        selectedFavorite: _selectedFavorite,
        onStatusChanged: _onStatusSelected,
        onFavoriteChanged: _onFavoriteSelected,
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'ترتيب النتائج',
          style: TextStyle(
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortOption('الأحدث أولاً', () => _sortBooks('newest')),
            _buildSortOption('الأقدم أولاً', () => _sortBooks('oldest')),
            _buildSortOption('الاسم (أ-ي)', () => _sortBooks('name_asc')),
            _buildSortOption('الاسم (ي-أ)', () => _sortBooks('name_desc')),
            _buildSortOption('المؤلف', () => _sortBooks('author')),
            _buildSortOption('التقييم', () => _sortBooks('rating')),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, VoidCallback onTap) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
      ),
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
    );
  }

  void _sortBooks(String sortType) {
    setState(() {
      switch (sortType) {
        case 'newest':
          _filteredBooks.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));
          break;
        case 'oldest':
          _filteredBooks.sort((a, b) => a.dateAdded.compareTo(b.dateAdded));
          break;
        case 'name_asc':
          _filteredBooks.sort((a, b) => a.title.compareTo(b.title));
          break;
        case 'name_desc':
          _filteredBooks.sort((a, b) => b.title.compareTo(a.title));
          break;
        case 'author':
          _filteredBooks.sort((a, b) => a.author.compareTo(b.author));
          break;
        case 'rating':
          _filteredBooks.sort((a, b) => b.rating.compareTo(a.rating));
          break;
      }
    });
  }
}

class _FilterBottomSheet extends StatefulWidget {
  final ReadingStatus? selectedStatus;
  final bool? selectedFavorite;
  final Function(ReadingStatus?) onStatusChanged;
  final Function(bool?) onFavoriteChanged;

  const _FilterBottomSheet({
    required this.selectedStatus,
    required this.selectedFavorite,
    required this.onStatusChanged,
    required this.onFavoriteChanged,
  });

  @override
  State<_FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<_FilterBottomSheet> {
  ReadingStatus? _tempStatus;
  bool? _tempFavorite;

  @override
  void initState() {
    super.initState();
    _tempStatus = widget.selectedStatus;
    _tempFavorite = widget.selectedFavorite;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'فلترة النتائج',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeXLarge,
                    fontWeight: FontWeight.bold,
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // فلتر حالة القراءة
            const Text(
              'حالة القراءة',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Wrap(
              spacing: AppConstants.paddingSmall,
              children: [
                _buildStatusChip('الكل', null),
                _buildStatusChip('أرغب بالقراءة', ReadingStatus.wantToRead),
                _buildStatusChip('جارٍ القراءة', ReadingStatus.reading),
                _buildStatusChip('مقروء', ReadingStatus.completed),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // فلتر المفضلة
            const Text(
              'المفضلة',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Wrap(
              spacing: AppConstants.paddingSmall,
              children: [
                _buildFavoriteChip('الكل', null),
                _buildFavoriteChip('المفضلة فقط', true),
                _buildFavoriteChip('غير المفضلة', false),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // أزرار التحكم
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _tempStatus = null;
                        _tempFavorite = null;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppConstants.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                      ),
                    ),
                    child: const Text(
                      'مسح الكل',
                      style: TextStyle(
                        color: AppConstants.primaryColor,
                        fontFamily: AppConstants.fontFamilyPrimary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onStatusChanged(_tempStatus);
                      widget.onFavoriteChanged(_tempFavorite);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                      ),
                    ),
                    child: const Text(
                      'تطبيق',
                      style: TextStyle(
                        fontFamily: AppConstants.fontFamilyPrimary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, ReadingStatus? status) {
    final isSelected = _tempStatus == status;
    return GestureDetector(
      onTap: () => setState(() => _tempStatus = status),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConstants.primaryColor 
              : AppConstants.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          border: Border.all(
            color: AppConstants.primaryColor,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : AppConstants.primaryColor,
            fontWeight: FontWeight.w500,
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildFavoriteChip(String label, bool? favorite) {
    final isSelected = _tempFavorite == favorite;
    return GestureDetector(
      onTap: () => setState(() => _tempFavorite = favorite),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConstants.errorColor 
              : AppConstants.errorColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          border: Border.all(
            color: AppConstants.errorColor,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : AppConstants.errorColor,
            fontWeight: FontWeight.w500,
            fontFamily: AppConstants.fontFamilyPrimary,
          ),
        ),
      ),
    );
  }
}
