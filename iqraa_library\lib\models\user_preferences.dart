import 'package:json_annotation/json_annotation.dart';

part 'user_preferences.g.dart';

@JsonSerializable()
class UserPreferences {
  final String fontFamily;
  final double fontSize;
  final bool isDarkMode;
  final bool isNightMode;
  final double brightness;
  final String defaultCategory;
  final bool autoBookmark;
  final int autoBookmarkInterval; // في الدقائق
  final ReadingDirection readingDirection;
  final PageTransition pageTransition;
  final bool showReadingProgress;
  final bool enableStatistics;

  UserPreferences({
    this.fontFamily = 'Cairo',
    this.fontSize = 16.0,
    this.isDarkMode = false,
    this.isNightMode = false,
    this.brightness = 1.0,
    this.defaultCategory = 'عام',
    this.autoBookmark = true,
    this.autoBookmarkInterval = 5,
    this.readingDirection = ReadingDirection.rightToLeft,
    this.pageTransition = PageTransition.slide,
    this.showReadingProgress = true,
    this.enableStatistics = true,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  UserPreferences copyWith({
    String? fontFamily,
    double? fontSize,
    bool? isDarkMode,
    bool? isNightMode,
    double? brightness,
    String? defaultCategory,
    bool? autoBookmark,
    int? autoBookmarkInterval,
    ReadingDirection? readingDirection,
    PageTransition? pageTransition,
    bool? showReadingProgress,
    bool? enableStatistics,
  }) {
    return UserPreferences(
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      isNightMode: isNightMode ?? this.isNightMode,
      brightness: brightness ?? this.brightness,
      defaultCategory: defaultCategory ?? this.defaultCategory,
      autoBookmark: autoBookmark ?? this.autoBookmark,
      autoBookmarkInterval: autoBookmarkInterval ?? this.autoBookmarkInterval,
      readingDirection: readingDirection ?? this.readingDirection,
      pageTransition: pageTransition ?? this.pageTransition,
      showReadingProgress: showReadingProgress ?? this.showReadingProgress,
      enableStatistics: enableStatistics ?? this.enableStatistics,
    );
  }
}

@JsonEnum()
enum ReadingDirection {
  @JsonValue('rtl')
  rightToLeft,
  @JsonValue('ltr')
  leftToRight,
}

@JsonEnum()
enum PageTransition {
  @JsonValue('slide')
  slide,
  @JsonValue('fade')
  fade,
  @JsonValue('scale')
  scale,
}

extension ReadingDirectionExtension on ReadingDirection {
  String get displayName {
    switch (this) {
      case ReadingDirection.rightToLeft:
        return 'من اليمين إلى اليسار';
      case ReadingDirection.leftToRight:
        return 'من اليسار إلى اليمين';
    }
  }
}

extension PageTransitionExtension on PageTransition {
  String get displayName {
    switch (this) {
      case PageTransition.slide:
        return 'انزلاق';
      case PageTransition.fade:
        return 'تلاشي';
      case PageTransition.scale:
        return 'تكبير';
    }
  }
}
