import 'package:flutter/material.dart';

class AppConstants {
  // معلومات التطبيق
  static const String appName = 'مكتبة إقرأ';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'مكتبة إلكترونية عربية شاملة';

  // الألوان
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color primaryLightColor = Color(0xFF60AD5E);
  static const Color primaryDarkColor = Color(0xFF005005);
  static const Color secondaryColor = Color(0xFF1976D2);
  static const Color accentColor = Color(0xFFFF9800);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color successColor = Color(0xFF388E3C);
  static const Color warningColor = Color(0xFFF57C00);
  static const Color infoColor = Color(0xFF1976D2);

  // الألوان الداكنة
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkSurfaceColor = Color(0xFF1E1E1E);
  static const Color darkPrimaryColor = Color(0xFF4CAF50);

  // أحجام الخطوط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;

  // المسافات
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // نصف الأقطار
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;

  // الارتفاعات
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;

  // أبعاد البطاقات
  static const double cardHeight = 200.0;
  static const double cardWidth = 150.0;
  static const double bookCoverAspectRatio = 0.7;

  // أبعاد الأيقونات
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // مدة الرسوم المتحركة
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);

  // أسماء الخطوط
  static const String fontFamilyPrimary = 'Cairo';
  static const String fontFamilySecondary = 'Tajawal';

  // مسارات الأصول
  static const String assetsImagesPath = 'assets/images/';
  static const String assetsBooksPath = 'assets/books/';
  static const String assetsIconsPath = 'assets/icons/';
  static const String assetsFontsPath = 'assets/fonts/';

  // أسماء الملفات
  static const String defaultBookCover = 'default_book_cover.png';
  static const String appLogo = 'app_logo.png';
  static const String noImagePlaceholder = 'no_image.png';

  // رسائل التطبيق
  static const String messageNoBooks = 'لا توجد كتب متاحة';
  static const String messageNoSearchResults = 'لا توجد نتائج للبحث';
  static const String messageBookAdded = 'تم إضافة الكتاب بنجاح';
  static const String messageBookDeleted = 'تم حذف الكتاب';
  static const String messageBookUpdated = 'تم تحديث الكتاب';
  static const String messageError = 'حدث خطأ غير متوقع';
  static const String messageNetworkError = 'تحقق من اتصال الإنترنت';
  static const String messageFileNotFound = 'الملف غير موجود';
  static const String messageInvalidFile = 'صيغة الملف غير صحيحة';

  // تسميات الواجهة
  static const String labelHome = 'الرئيسية';
  static const String labelSearch = 'البحث';
  static const String labelLibrary = 'المكتبة';
  static const String labelFavorites = 'المفضلة';
  static const String labelSettings = 'الإعدادات';
  static const String labelAddBook = 'إضافة كتاب';
  static const String labelBookDetails = 'تفاصيل الكتاب';
  static const String labelReadBook = 'قراءة الكتاب';
  static const String labelCategories = 'التصنيفات';
  static const String labelTags = 'العلامات';
  static const String labelAuthor = 'المؤلف';
  static const String labelTitle = 'العنوان';
  static const String labelDescription = 'الوصف';
  static const String labelCategory = 'التصنيف';
  static const String labelRating = 'التقييم';
  static const String labelProgress = 'التقدم';
  static const String labelPages = 'الصفحات';
  static const String labelReadingTime = 'وقت القراءة';
  static const String labelLastRead = 'آخر قراءة';
  static const String labelDateAdded = 'تاريخ الإضافة';

  // أزرار
  static const String buttonAdd = 'إضافة';
  static const String buttonEdit = 'تعديل';
  static const String buttonDelete = 'حذف';
  static const String buttonSave = 'حفظ';
  static const String buttonCancel = 'إلغاء';
  static const String buttonOk = 'موافق';
  static const String buttonYes = 'نعم';
  static const String buttonNo = 'لا';
  static const String buttonRead = 'قراءة';
  static const String buttonContinueReading = 'متابعة القراءة';
  static const String buttonStartReading = 'بدء القراءة';
  static const String buttonMarkAsRead = 'تحديد كمقروء';
  static const String buttonAddToFavorites = 'إضافة للمفضلة';
  static const String buttonRemoveFromFavorites = 'إزالة من المفضلة';
  static const String buttonShare = 'مشاركة';
  static const String buttonExport = 'تصدير';
  static const String buttonImport = 'استيراد';

  // حالات القراءة
  static const String statusWantToRead = 'أرغب بالقراءة';
  static const String statusReading = 'جارٍ القراءة';
  static const String statusCompleted = 'مقروء';

  // التصنيفات الافتراضية
  static const List<String> defaultCategories = [
    'ديني',
    'تنمية بشرية',
    'روايات',
    'تاريخ',
    'علوم',
    'فلسفة',
    'أدب',
    'شعر',
    'سيرة ذاتية',
    'تكنولوجيا',
    'طب',
    'قانون',
    'اقتصاد',
    'سياسة',
    'تعليم',
    'عام',
  ];

  // أيقونات التصنيفات
  static const Map<String, IconData> categoryIcons = {
    'ديني': Icons.mosque,
    'تنمية بشرية': Icons.psychology,
    'روايات': Icons.auto_stories,
    'تاريخ': Icons.history_edu,
    'علوم': Icons.science,
    'فلسفة': Icons.lightbulb,
    'أدب': Icons.menu_book,
    'شعر': Icons.format_quote,
    'سيرة ذاتية': Icons.person,
    'تكنولوجيا': Icons.computer,
    'طب': Icons.medical_services,
    'قانون': Icons.gavel,
    'اقتصاد': Icons.trending_up,
    'سياسة': Icons.account_balance,
    'تعليم': Icons.school,
    'عام': Icons.book,
  };

  // ألوان التصنيفات
  static const Map<String, Color> categoryColors = {
    'ديني': Color(0xFF4CAF50),
    'تنمية بشرية': Color(0xFF2196F3),
    'روايات': Color(0xFF9C27B0),
    'تاريخ': Color(0xFF795548),
    'علوم': Color(0xFF00BCD4),
    'فلسفة': Color(0xFFFF9800),
    'أدب': Color(0xFFE91E63),
    'شعر': Color(0xFF673AB7),
    'سيرة ذاتية': Color(0xFF607D8B),
    'تكنولوجيا': Color(0xFF3F51B5),
    'طب': Color(0xFFF44336),
    'قانون': Color(0xFF9E9E9E),
    'اقتصاد': Color(0xFF4CAF50),
    'سياسة': Color(0xFF2196F3),
    'تعليم': Color(0xFFFF5722),
    'عام': Color(0xFF757575),
  };

  // إعدادات PDF
  static const double pdfMinZoom = 0.5;
  static const double pdfMaxZoom = 3.0;
  static const double pdfDefaultZoom = 1.0;
  static const int pdfCacheSize = 50; // عدد الصفحات المحفوظة في الذاكرة

  // إعدادات البحث
  static const int searchResultsLimit = 50;
  static const int searchHistoryLimit = 20;

  // إعدادات الإحصائيات
  static const int statisticsHistoryDays = 30;
  static const int readingGoalDefaultPages = 20;
  static const int readingGoalDefaultMinutes = 30;

  // إعدادات التصدير والاستيراد
  static const String exportFileExtension = '.json';
  static const String backupFilePrefix = 'iqraa_backup_';

  // إعدادات الإشعارات
  static const String notificationChannelId = 'iqraa_notifications';
  static const String notificationChannelName = 'إشعارات إقرأ';
  static const String notificationChannelDescription = 'إشعارات تطبيق مكتبة إقرأ';

  // روابط مفيدة
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://iqraa.app';
  static const String privacyPolicyUrl = 'https://iqraa.app/privacy';
  static const String termsOfServiceUrl = 'https://iqraa.app/terms';

  // إعدادات التطبيق
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;
  static const bool enableAutoBackup = true;
  static const int autoBackupIntervalDays = 7;
}
