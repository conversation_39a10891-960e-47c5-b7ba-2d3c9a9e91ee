import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'constants.dart';

class AppHelpers {
  // تنسيق التاريخ
  static String formatDate(DateTime date, {String? pattern}) {
    final formatter = DateFormat(pattern ?? 'dd/MM/yyyy', 'ar');
    return formatter.format(date);
  }

  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime, {String? pattern}) {
    final formatter = DateFormat(pattern ?? 'dd/MM/yyyy HH:mm', 'ar');
    return formatter.format(dateTime);
  }

  // تنسيق الوقت النسبي (منذ كم يوم)
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${_getDayText(difference.inDays)}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${_getHourText(difference.inHours)}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${_getMinuteText(difference.inMinutes)}';
    } else {
      return 'الآن';
    }
  }

  static String _getDayText(int days) {
    if (days == 1) return 'يوم';
    if (days == 2) return 'يومين';
    if (days <= 10) return 'أيام';
    return 'يوماً';
  }

  static String _getHourText(int hours) {
    if (hours == 1) return 'ساعة';
    if (hours == 2) return 'ساعتين';
    if (hours <= 10) return 'ساعات';
    return 'ساعة';
  }

  static String _getMinuteText(int minutes) {
    if (minutes == 1) return 'دقيقة';
    if (minutes == 2) return 'دقيقتين';
    if (minutes <= 10) return 'دقائق';
    return 'دقيقة';
  }

  // تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
  }

  // تنسيق النسبة المئوية
  static String formatPercentage(double value, {int decimals = 1}) {
    return '${value.toStringAsFixed(decimals)}%';
  }

  // تنسيق وقت القراءة
  static String formatReadingTime(int minutes) {
    if (minutes < 60) {
      return '$minutes دقيقة';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${_getHourText(hours)}';
      } else {
        return '$hours ${_getHourText(hours)} و $remainingMinutes ${_getMinuteText(remainingMinutes)}';
      }
    }
  }

  // تنسيق عدد الصفحات
  static String formatPageCount(int pages) {
    if (pages == 1) return 'صفحة واحدة';
    if (pages == 2) return 'صفحتان';
    if (pages <= 10) return '$pages صفحات';
    return '$pages صفحة';
  }

  // تنسيق التقييم
  static String formatRating(double rating) {
    return rating.toStringAsFixed(1);
  }

  // الحصول على لون التصنيف
  static Color getCategoryColor(String category) {
    return AppConstants.categoryColors[category] ?? AppConstants.primaryColor;
  }

  // الحصول على أيقونة التصنيف
  static IconData getCategoryIcon(String category) {
    return AppConstants.categoryIcons[category] ?? Icons.book;
  }

  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // التحقق من صحة رقم الهاتف
  static bool isValidPhoneNumber(String phone) {
    return RegExp(r'^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$').hasMatch(phone);
  }

  // تنظيف النص
  static String cleanText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  // اقتطاع النص
  static String truncateText(String text, int maxLength, {String suffix = '...'}) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  // تحويل النص إلى عنوان URL
  static String textToSlug(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'[-\s]+'), '-');
  }

  // إنشاء معرف فريد
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // تحويل الألوان
  static Color hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }

  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  // حساب التباين
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  // إظهار رسالة نجاح
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
      ),
    );
  }

  // إظهار رسالة خطأ
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
      ),
    );
  }

  // إظهار رسالة تحذير
  static void showWarningMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.warningColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
      ),
    );
  }

  // إظهار رسالة معلومات
  static void showInfoMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.infoColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
      ),
    );
  }

  // إظهار حوار تأكيد
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = AppConstants.buttonYes,
    String cancelText = AppConstants.buttonNo,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // إظهار حوار تحميل
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            if (message != null) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Text(message),
            ],
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
      ),
    );
  }

  // إخفاء حوار التحميل
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  // التنقل إلى صفحة جديدة
  static Future<T?> navigateTo<T>(BuildContext context, Widget page) {
    return Navigator.of(context).push<T>(
      MaterialPageRoute(builder: (context) => page),
    );
  }

  // التنقل مع استبدال الصفحة الحالية
  static Future<T?> navigateAndReplace<T>(BuildContext context, Widget page) {
    return Navigator.of(context).pushReplacement<T, void>(
      MaterialPageRoute(builder: (context) => page),
    );
  }

  // التنقل وإزالة جميع الصفحات السابقة
  static Future<T?> navigateAndClearStack<T>(BuildContext context, Widget page) {
    return Navigator.of(context).pushAndRemoveUntil<T>(
      MaterialPageRoute(builder: (context) => page),
      (route) => false,
    );
  }

  // الرجوع للصفحة السابقة
  static void goBack(BuildContext context, [dynamic result]) {
    Navigator.of(context).pop(result);
  }

  // التحقق من إمكانية الرجوع
  static bool canGoBack(BuildContext context) {
    return Navigator.of(context).canPop();
  }

  // إنشاء تدرج لوني
  static LinearGradient createGradient({
    required Color startColor,
    required Color endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
    );
  }

  // إنشاء ظل
  static List<BoxShadow> createShadow({
    Color color = Colors.black26,
    double blurRadius = 4.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
  }) {
    return [
      BoxShadow(
        color: color,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  // تحويل قائمة إلى خريطة
  static Map<K, V> listToMap<T, K, V>(
    List<T> list,
    K Function(T) keyMapper,
    V Function(T) valueMapper,
  ) {
    final map = <K, V>{};
    for (final item in list) {
      map[keyMapper(item)] = valueMapper(item);
    }
    return map;
  }

  // تجميع قائمة حسب مفتاح
  static Map<K, List<T>> groupBy<T, K>(
    List<T> list,
    K Function(T) keyMapper,
  ) {
    final map = <K, List<T>>{};
    for (final item in list) {
      final key = keyMapper(item);
      map.putIfAbsent(key, () => []).add(item);
    }
    return map;
  }

  // ترتيب قائمة
  static List<T> sortList<T>(
    List<T> list,
    int Function(T, T) compare,
  ) {
    final sortedList = List<T>.from(list);
    sortedList.sort(compare);
    return sortedList;
  }

  // فلترة قائمة
  static List<T> filterList<T>(
    List<T> list,
    bool Function(T) predicate,
  ) {
    return list.where(predicate).toList();
  }

  // البحث في قائمة
  static List<T> searchInList<T>(
    List<T> list,
    String query,
    String Function(T) textExtractor,
  ) {
    if (query.isEmpty) return list;
    
    final lowerQuery = query.toLowerCase();
    return list.where((item) {
      final text = textExtractor(item).toLowerCase();
      return text.contains(lowerQuery);
    }).toList();
  }

  // تحديد الاتجاه حسب اللغة
  static TextDirection getTextDirection(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text) ? TextDirection.rtl : TextDirection.ltr;
  }

  // تحديد ما إذا كان النص عربياً
  static bool isArabicText(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  // تنسيق الأرقام العربية
  static String formatArabicNumbers(String text) {
    const englishNumbers = '0123456789';
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    
    String result = text;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }

  // تحويل الأرقام العربية إلى إنجليزية
  static String convertArabicToEnglishNumbers(String text) {
    const englishNumbers = '0123456789';
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    
    String result = text;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }
}
