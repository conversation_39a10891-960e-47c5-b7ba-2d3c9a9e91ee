import 'package:flutter/material.dart';
import '../utils/constants.dart';

class ReadingProgressIndicator extends StatelessWidget {
  final double progress;
  final double height;
  final Color? backgroundColor;
  final Color? progressColor;
  final BorderRadius? borderRadius;
  final bool showPercentage;
  final TextStyle? percentageStyle;

  const ReadingProgressIndicator({
    super.key,
    required this.progress,
    this.height = 8.0,
    this.backgroundColor,
    this.progressColor,
    this.borderRadius,
    this.showPercentage = false,
    this.percentageStyle,
  });

  @override
  Widget build(BuildContext context) {
    final clampedProgress = progress.clamp(0.0, 1.0);
    final effectiveBackgroundColor = backgroundColor ?? Colors.grey[300]!;
    final effectiveProgressColor = progressColor ?? AppConstants.primaryColor;
    final effectiveBorderRadius = borderRadius ?? 
        BorderRadius.circular(height / 2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: height,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: effectiveBorderRadius,
          ),
          child: ClipRRect(
            borderRadius: effectiveBorderRadius,
            child: LinearProgressIndicator(
              value: clampedProgress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
            ),
          ),
        ),
        if (showPercentage) ...[
          const SizedBox(height: 4),
          Text(
            '${(clampedProgress * 100).toStringAsFixed(0)}%',
            style: percentageStyle ?? TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: Colors.grey[600],
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ],
    );
  }
}

class CircularReadingProgress extends StatelessWidget {
  final double progress;
  final double size;
  final double strokeWidth;
  final Color? backgroundColor;
  final Color? progressColor;
  final Widget? child;
  final bool showPercentage;
  final TextStyle? percentageStyle;

  const CircularReadingProgress({
    super.key,
    required this.progress,
    this.size = 60.0,
    this.strokeWidth = 6.0,
    this.backgroundColor,
    this.progressColor,
    this.child,
    this.showPercentage = true,
    this.percentageStyle,
  });

  @override
  Widget build(BuildContext context) {
    final clampedProgress = progress.clamp(0.0, 1.0);
    final effectiveBackgroundColor = backgroundColor ?? Colors.grey[300]!;
    final effectiveProgressColor = progressColor ?? AppConstants.primaryColor;

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            value: clampedProgress,
            strokeWidth: strokeWidth,
            backgroundColor: effectiveBackgroundColor,
            valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
          ),
          if (child != null)
            child!
          else if (showPercentage)
            Text(
              '${(clampedProgress * 100).toStringAsFixed(0)}%',
              style: percentageStyle ?? TextStyle(
                fontSize: size * 0.2,
                fontWeight: FontWeight.bold,
                color: effectiveProgressColor,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
        ],
      ),
    );
  }
}

class AnimatedReadingProgress extends StatefulWidget {
  final double progress;
  final double height;
  final Duration animationDuration;
  final Color? backgroundColor;
  final Color? progressColor;
  final BorderRadius? borderRadius;
  final bool showPercentage;
  final TextStyle? percentageStyle;

  const AnimatedReadingProgress({
    super.key,
    required this.progress,
    this.height = 8.0,
    this.animationDuration = const Duration(milliseconds: 800),
    this.backgroundColor,
    this.progressColor,
    this.borderRadius,
    this.showPercentage = false,
    this.percentageStyle,
  });

  @override
  State<AnimatedReadingProgress> createState() => _AnimatedReadingProgressState();
}

class _AnimatedReadingProgressState extends State<AnimatedReadingProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress.clamp(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void didUpdateWidget(AnimatedReadingProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.progress.clamp(0.0, 1.0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      _animationController.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ReadingProgressIndicator(
          progress: _animation.value,
          height: widget.height,
          backgroundColor: widget.backgroundColor,
          progressColor: widget.progressColor,
          borderRadius: widget.borderRadius,
          showPercentage: widget.showPercentage,
          percentageStyle: widget.percentageStyle,
        );
      },
    );
  }
}

class GradientReadingProgress extends StatelessWidget {
  final double progress;
  final double height;
  final List<Color> gradientColors;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool showPercentage;
  final TextStyle? percentageStyle;

  const GradientReadingProgress({
    super.key,
    required this.progress,
    this.height = 8.0,
    this.gradientColors = const [
      AppConstants.primaryColor,
      AppConstants.primaryLightColor,
    ],
    this.backgroundColor,
    this.borderRadius,
    this.showPercentage = false,
    this.percentageStyle,
  });

  @override
  Widget build(BuildContext context) {
    final clampedProgress = progress.clamp(0.0, 1.0);
    final effectiveBackgroundColor = backgroundColor ?? Colors.grey[300]!;
    final effectiveBorderRadius = borderRadius ?? 
        BorderRadius.circular(height / 2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: height,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: effectiveBorderRadius,
          ),
          child: ClipRRect(
            borderRadius: effectiveBorderRadius,
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: height,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: gradientColors,
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                ),
                Positioned.fill(
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: clampedProgress,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: gradientColors,
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (showPercentage) ...[
          const SizedBox(height: 4),
          Text(
            '${(clampedProgress * 100).toStringAsFixed(0)}%',
            style: percentageStyle ?? TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: Colors.grey[600],
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ],
    );
  }
}

class ReadingProgressCard extends StatelessWidget {
  final double progress;
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;

  const ReadingProgressCard({
    super.key,
    required this.progress,
    required this.title,
    this.subtitle,
    this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppConstants.primaryColor;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: effectiveColor,
                    size: AppConstants.iconSizeMedium,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                ],
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.bold,
                      color: effectiveColor,
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                  ),
                ),
                Text(
                  '${(progress * 100).toStringAsFixed(0)}%',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                    color: effectiveColor,
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                ),
              ],
            ),
            if (subtitle != null) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: Colors.grey[600],
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ],
            const SizedBox(height: AppConstants.paddingMedium),
            AnimatedReadingProgress(
              progress: progress,
              height: 6,
              progressColor: effectiveColor,
              animationDuration: const Duration(milliseconds: 1000),
            ),
          ],
        ),
      ),
    );
  }
}
