import 'package:json_annotation/json_annotation.dart';

part 'reading_progress.g.dart';

@JsonSerializable()
class ReadingProgress {
  final String bookId;
  final int currentPage;
  final int totalPages;
  final DateTime lastReadDate;
  final int readingTimeMinutes;
  final List<BookmarkPage> bookmarks;
  final Map<String, dynamic> readerSettings;

  ReadingProgress({
    required this.bookId,
    required this.currentPage,
    required this.totalPages,
    required this.lastReadDate,
    this.readingTimeMinutes = 0,
    this.bookmarks = const [],
    this.readerSettings = const {},
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) =>
      _$ReadingProgressFromJson(json);
  Map<String, dynamic> toJson() => _$ReadingProgressToJson(this);

  ReadingProgress copyWith({
    String? bookId,
    int? currentPage,
    int? totalPages,
    DateTime? lastReadDate,
    int? readingTimeMinutes,
    List<BookmarkPage>? bookmarks,
    Map<String, dynamic>? readerSettings,
  }) {
    return ReadingProgress(
      bookId: bookId ?? this.bookId,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      lastReadDate: lastReadDate ?? this.lastReadDate,
      readingTimeMinutes: readingTimeMinutes ?? this.readingTimeMinutes,
      bookmarks: bookmarks ?? this.bookmarks,
      readerSettings: readerSettings ?? this.readerSettings,
    );
  }

  double get progressPercentage {
    if (totalPages == 0) return 0.0;
    return (currentPage / totalPages) * 100;
  }

  bool get isCompleted => currentPage >= totalPages;
}

@JsonSerializable()
class BookmarkPage {
  final int pageNumber;
  final String note;
  final DateTime createdAt;

  BookmarkPage({
    required this.pageNumber,
    required this.note,
    required this.createdAt,
  });

  factory BookmarkPage.fromJson(Map<String, dynamic> json) =>
      _$BookmarkPageFromJson(json);
  Map<String, dynamic> toJson() => _$BookmarkPageToJson(this);
}
