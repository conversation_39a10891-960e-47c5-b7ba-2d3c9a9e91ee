// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReadingProgress _$ReadingProgressFromJson(Map<String, dynamic> json) =>
    ReadingProgress(
      bookId: json['bookId'] as String,
      currentPage: (json['currentPage'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      lastReadDate: DateTime.parse(json['lastReadDate'] as String),
      readingTimeMinutes: (json['readingTimeMinutes'] as num?)?.toInt() ?? 0,
      bookmarks: (json['bookmarks'] as List<dynamic>?)
              ?.map((e) => BookmarkPage.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      readerSettings:
          json['readerSettings'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$ReadingProgressToJson(ReadingProgress instance) =>
    <String, dynamic>{
      'bookId': instance.bookId,
      'currentPage': instance.currentPage,
      'totalPages': instance.totalPages,
      'lastReadDate': instance.lastReadDate.toIso8601String(),
      'readingTimeMinutes': instance.readingTimeMinutes,
      'bookmarks': instance.bookmarks,
      'readerSettings': instance.readerSettings,
    };

BookmarkPage _$BookmarkPageFromJson(Map<String, dynamic> json) => BookmarkPage(
      pageNumber: (json['pageNumber'] as num).toInt(),
      note: json['note'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$BookmarkPageToJson(BookmarkPage instance) =>
    <String, dynamic>{
      'pageNumber': instance.pageNumber,
      'note': instance.note,
      'createdAt': instance.createdAt.toIso8601String(),
    };
