import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'services/storage_service.dart';
import 'services/book_service.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الخدمات
  await StorageService.instance.init();

  // إنشاء كتب تجريبية إذا لم تكن موجودة
  final books = await BookService.instance.getAllBooks();
  if (books.isEmpty) {
    await BookService.instance.createSampleBooks();
  }

  runApp(const IqraaApp());
}

class IqraaApp extends StatelessWidget {
  const IqraaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // الثيم
      theme: _buildLightTheme(),
      darkTheme: _buildDarkTheme(),
      themeMode: ThemeMode.system,

      // الصفحة الرئيسية
      home: const HomeScreen(),

      // إعدادات النظام
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );
  }

  ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: AppConstants.fontFamilyPrimary,

      // الألوان
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
      ),

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontFamily: AppConstants.fontFamilyPrimary,
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),

      // حقول النص
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
        labelStyle: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
        hintStyle: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          color: Colors.grey[500],
        ),
      ),

      // البطاقات
      cardTheme: CardTheme(
        elevation: AppConstants.elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),

      // النصوص
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeHeading,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.bold,
        ),
        headlineLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXXLarge,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeLarge,
        ),
        bodyMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeMedium,
        ),
        bodySmall: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeSmall,
        ),
      ),
    );
  }

  ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: AppConstants.fontFamilyPrimary,
      brightness: Brightness.dark,

      // الألوان
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.darkPrimaryColor,
        brightness: Brightness.dark,
        surface: AppConstants.darkSurfaceColor,
      ),

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.darkSurfaceColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.darkPrimaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontFamily: AppConstants.fontFamilyPrimary,
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),

      // حقول النص
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppConstants.darkPrimaryColor,
            width: 2,
          ),
        ),
        labelStyle: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
        hintStyle: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          color: Colors.grey[400],
        ),
      ),

      // البطاقات
      cardTheme: CardTheme(
        elevation: AppConstants.elevationMedium,
        color: AppConstants.darkSurfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),

      // النصوص
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeHeading,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        displayMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        headlineLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXXLarge,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        headlineMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
        bodyLarge: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeLarge,
          color: Colors.white70,
        ),
        bodyMedium: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeMedium,
          color: Colors.white70,
        ),
        bodySmall: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontSize: AppConstants.fontSizeSmall,
          color: Colors.white60,
        ),
      ),
    );
  }
}
