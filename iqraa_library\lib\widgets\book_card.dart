import 'package:flutter/material.dart';
import '../models/book.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import 'reading_progress_indicator.dart';

class BookCard extends StatelessWidget {
  final Book book;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final bool showProgress;
  final bool showFavoriteButton;

  const BookCard({
    super.key,
    required this.book,
    this.onTap,
    this.onFavoriteToggle,
    this.showProgress = true,
    this.showFavoriteButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: AppHelpers.createShadow(
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCoverSection(context),
            _buildInfoSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverSection(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusLarge),
            topRight: Radius.circular(AppConstants.radiusLarge),
          ),
          gradient: AppHelpers.createGradient(
            startColor: AppHelpers.getCategoryColor(book.category).withOpacity(0.3),
            endColor: AppHelpers.getCategoryColor(book.category).withOpacity(0.1),
          ),
        ),
        child: Stack(
          children: [
            // صورة الغلاف أو الأيقونة الافتراضية
            Center(
              child: book.coverImagePath.isNotEmpty
                  ? _buildCoverImage()
                  : _buildDefaultCover(),
            ),
            
            // زر المفضلة
            if (showFavoriteButton)
              Positioned(
                top: AppConstants.paddingSmall,
                right: AppConstants.paddingSmall,
                child: _buildFavoriteButton(),
              ),
            
            // شارة حالة القراءة
            Positioned(
              top: AppConstants.paddingSmall,
              left: AppConstants.paddingSmall,
              child: _buildStatusBadge(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverImage() {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(AppConstants.radiusLarge),
        topRight: Radius.circular(AppConstants.radiusLarge),
      ),
      child: Image.asset(
        book.coverImagePath,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildDefaultCover(),
      ),
    );
  }

  Widget _buildDefaultCover() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: AppHelpers.createGradient(
          startColor: AppHelpers.getCategoryColor(book.category).withOpacity(0.6),
          endColor: AppHelpers.getCategoryColor(book.category).withOpacity(0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            AppHelpers.getCategoryIcon(book.category),
            size: AppConstants.iconSizeXLarge,
            color: Colors.white,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
            child: Text(
              book.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return GestureDetector(
      onTap: onFavoriteToggle,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          shape: BoxShape.circle,
          boxShadow: AppHelpers.createShadow(blurRadius: 4),
        ),
        child: Icon(
          book.isFavorite ? Icons.favorite : Icons.favorite_border,
          color: book.isFavorite ? AppConstants.errorColor : Colors.grey,
          size: AppConstants.iconSizeMedium,
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color badgeColor;
    String badgeText;
    IconData badgeIcon;

    switch (book.readingStatus) {
      case ReadingStatus.reading:
        badgeColor = AppConstants.infoColor;
        badgeText = 'جارٍ';
        badgeIcon = Icons.auto_stories;
        break;
      case ReadingStatus.completed:
        badgeColor = AppConstants.successColor;
        badgeText = 'مقروء';
        badgeIcon = Icons.check_circle;
        break;
      case ReadingStatus.wantToRead:
        badgeColor = AppConstants.warningColor;
        badgeText = 'قريباً';
        badgeIcon = Icons.bookmark_border;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            color: Colors.white,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            badgeText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Expanded(
      flex: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الكتاب
            Text(
              book.title,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            // اسم المؤلف
            Text(
              book.author,
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: Colors.grey[600],
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            // التصنيف
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingSmall,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppHelpers.getCategoryColor(book.category).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                border: Border.all(
                  color: AppHelpers.getCategoryColor(book.category).withOpacity(0.3),
                ),
              ),
              child: Text(
                book.category,
                style: TextStyle(
                  fontSize: 10,
                  color: AppHelpers.getCategoryColor(book.category),
                  fontWeight: FontWeight.bold,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ),
            
            const Spacer(),
            
            // مؤشر التقدم
            if (showProgress && book.readingStatus == ReadingStatus.reading) ...[
              ReadingProgressIndicator(
                progress: book.readingProgress,
                height: 4,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '${AppHelpers.formatPercentage(book.readingProgress * 100)} مكتمل',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
            ],
            
            // التقييم
            if (book.rating > 0) ...[
              Row(
                children: [
                  ...List.generate(5, (index) {
                    return Icon(
                      index < book.rating ? Icons.star : Icons.star_border,
                      color: AppConstants.warningColor,
                      size: 12,
                    );
                  }),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    AppHelpers.formatRating(book.rating),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[600],
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
