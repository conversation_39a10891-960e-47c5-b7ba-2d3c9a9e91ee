// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'book.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Book _$BookFromJson(Map<String, dynamic> json) => Book(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      coverImagePath: json['coverImagePath'] as String,
      pdfPath: json['pdfPath'] as String,
      dateAdded: DateTime.parse(json['dateAdded'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      readingStatus:
          $enumDecodeNullable(_$ReadingStatusEnumMap, json['readingStatus']) ??
              ReadingStatus.wantToRead,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 0,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$BookToJson(Book instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'author': instance.author,
      'description': instance.description,
      'category': instance.category,
      'tags': instance.tags,
      'coverImagePath': instance.coverImagePath,
      'pdfPath': instance.pdfPath,
      'dateAdded': instance.dateAdded.toIso8601String(),
      'isFavorite': instance.isFavorite,
      'readingStatus': _$ReadingStatusEnumMap[instance.readingStatus]!,
      'totalPages': instance.totalPages,
      'currentPage': instance.currentPage,
      'rating': instance.rating,
    };

const _$ReadingStatusEnumMap = {
  ReadingStatus.wantToRead: 'want_to_read',
  ReadingStatus.reading: 'reading',
  ReadingStatus.completed: 'completed',
};
