import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../models/book.dart';
import '../services/book_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/category_chip.dart';

class AddBookScreen extends StatefulWidget {
  final Book? bookToEdit;

  const AddBookScreen({
    super.key,
    this.bookToEdit,
  });

  @override
  State<AddBookScreen> createState() => _AddBookScreenState();
}

class _AddBookScreenState extends State<AddBookScreen> {
  final BookService _bookService = BookService.instance;
  final StorageService _storage = StorageService.instance;
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _titleController = TextEditingController();
  final _authorController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagController = TextEditingController();

  // State variables
  List<String> _categories = [];
  String _selectedCategory = '';
  List<String> _tags = [];
  String? _selectedPdfPath;
  String? _selectedCoverPath;
  bool _isLoading = false;
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.bookToEdit != null;
    _loadCategories();
    if (_isEditMode) {
      _populateFields();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _authorController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _storage.getCategories();
      setState(() {
        _categories = categories;
        if (_categories.isNotEmpty && _selectedCategory.isEmpty) {
          _selectedCategory = _categories.first;
        }
      });
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في تحميل التصنيفات');
    }
  }

  void _populateFields() {
    final book = widget.bookToEdit!;
    _titleController.text = book.title;
    _authorController.text = book.author;
    _descriptionController.text = book.description;
    _selectedCategory = book.category;
    _tags = List.from(book.tags);
    _selectedPdfPath = book.pdfPath;
    _selectedCoverPath = book.coverImagePath;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isEditMode ? 'تعديل الكتاب' : 'إضافة كتاب جديد',
        style: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (_isEditMode)
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _showDeleteDialog,
            tooltip: 'حذف الكتاب',
          ),
      ],
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFileSelectionSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildBasicInfoSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildCategorySection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildTagsSection(),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildDescriptionSection(),
            const SizedBox(height: 100), // مساحة للشريط السفلي
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الملفات',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // اختيار ملف PDF
          _buildFileSelector(
            title: 'ملف PDF',
            subtitle: _selectedPdfPath != null 
                ? 'تم اختيار الملف'
                : 'اختر ملف PDF للكتاب',
            icon: Icons.picture_as_pdf,
            color: AppConstants.errorColor,
            isSelected: _selectedPdfPath != null,
            onTap: _selectPdfFile,
            isRequired: !_isEditMode,
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // اختيار صورة الغلاف
          _buildFileSelector(
            title: 'صورة الغلاف',
            subtitle: _selectedCoverPath != null 
                ? 'تم اختيار الصورة'
                : 'اختر صورة غلاف للكتاب (اختياري)',
            icon: Icons.image,
            color: AppConstants.infoColor,
            isSelected: _selectedCoverPath != null,
            onTap: _selectCoverImage,
            isRequired: false,
          ),
        ],
      ),
    );
  }

  Widget _buildFileSelector({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
    required bool isRequired,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: isSelected 
              ? color.withOpacity(0.1) 
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          border: Border.all(
            color: isSelected 
                ? color 
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: AppConstants.iconSizeLarge,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? color : Colors.black87,
                          fontFamily: AppConstants.fontFamilyPrimary,
                        ),
                      ),
                      if (isRequired) ...[
                        const SizedBox(width: 4),
                        const Text(
                          '*',
                          style: TextStyle(
                            color: AppConstants.errorColor,
                            fontSize: AppConstants.fontSizeMedium,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: Colors.grey[600],
                      fontFamily: AppConstants.fontFamilyPrimary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isSelected ? Icons.check_circle : Icons.add_circle_outline,
              color: isSelected ? color : Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المعلومات الأساسية',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // عنوان الكتاب
          _buildTextField(
            controller: _titleController,
            label: 'عنوان الكتاب',
            hint: 'أدخل عنوان الكتاب',
            icon: Icons.book,
            isRequired: true,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'عنوان الكتاب مطلوب';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // اسم المؤلف
          _buildTextField(
            controller: _authorController,
            label: 'اسم المؤلف',
            hint: 'أدخل اسم المؤلف',
            icon: Icons.person,
            isRequired: true,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'اسم المؤلف مطلوب';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required bool isRequired,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      textDirection: TextDirection.rtl,
      maxLines: maxLines,
      style: const TextStyle(
        fontFamily: AppConstants.fontFamilyPrimary,
      ),
      decoration: InputDecoration(
        labelText: label + (isRequired ? ' *' : ''),
        hintText: hint,
        prefixIcon: Icon(icon, color: AppConstants.primaryColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
        labelStyle: const TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
        ),
        hintStyle: TextStyle(
          fontFamily: AppConstants.fontFamilyPrimary,
          color: Colors.grey[500],
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildCategorySection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'التصنيف',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  fontFamily: AppConstants.fontFamilyPrimary,
                ),
              ),
              TextButton.icon(
                onPressed: _showAddCategoryDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة تصنيف'),
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          if (_categories.isNotEmpty)
            Wrap(
              spacing: AppConstants.paddingSmall,
              runSpacing: AppConstants.paddingSmall,
              children: _categories.map((category) {
                return CategoryChip(
                  category: category,
                  isSelected: _selectedCategory == category,
                  onTap: () => setState(() => _selectedCategory = category),
                  showCount: false,
                );
              }).toList(),
            )
          else
            const Text(
              'لا توجد تصنيفات متاحة',
              style: TextStyle(
                color: Colors.grey,
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'العلامات',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // حقل إضافة علامة
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _tagController,
                  textDirection: TextDirection.rtl,
                  style: const TextStyle(
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: 'أضف علامة جديدة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 2,
                      ),
                    ),
                    hintStyle: TextStyle(
                      fontFamily: AppConstants.fontFamilyPrimary,
                      color: Colors.grey[500],
                    ),
                  ),
                  onSubmitted: _addTag,
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              IconButton(
                onPressed: () => _addTag(_tagController.text),
                icon: const Icon(Icons.add),
                style: IconButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // عرض العلامات المضافة
          if (_tags.isNotEmpty)
            Wrap(
              spacing: AppConstants.paddingSmall,
              runSpacing: AppConstants.paddingSmall,
              children: _tags.map((tag) => _buildTagChip(tag)).toList(),
            )
          else
            Text(
              'لم يتم إضافة علامات بعد',
              style: TextStyle(
                color: Colors.grey[600],
                fontFamily: AppConstants.fontFamilyPrimary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTagChip(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: AppConstants.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tag,
            style: const TextStyle(
              color: AppConstants.primaryColor,
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          GestureDetector(
            onTap: () => _removeTag(tag),
            child: const Icon(
              Icons.close,
              color: AppConstants.primaryColor,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: AppHelpers.createShadow(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الوصف',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              fontFamily: AppConstants.fontFamilyPrimary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildTextField(
            controller: _descriptionController,
            label: 'وصف الكتاب',
            hint: 'أدخل وصفاً مختصراً عن الكتاب',
            icon: Icons.description,
            isRequired: false,
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppConstants.primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.paddingLarge,
                  ),
                ),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontSize: AppConstants.fontSizeLarge,
                    fontFamily: AppConstants.fontFamilyPrimary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveBook,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.paddingLarge,
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        _isEditMode ? 'حفظ التغييرات' : 'إضافة الكتاب',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          fontFamily: AppConstants.fontFamilyPrimary,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectPdfFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedPdfPath = result.files.first.path;
        });
        AppHelpers.showSuccessMessage(context, 'تم اختيار ملف PDF بنجاح');
      }
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في اختيار الملف');
    }
  }

  Future<void> _selectCoverImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedCoverPath = result.files.first.path;
        });
        AppHelpers.showSuccessMessage(context, 'تم اختيار صورة الغلاف بنجاح');
      }
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في اختيار الصورة');
    }
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isNotEmpty && !_tags.contains(trimmedTag)) {
      setState(() {
        _tags.add(trimmedTag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _showAddCategoryDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'إضافة تصنيف جديد',
          style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
        ),
        content: TextField(
          controller: controller,
          textDirection: TextDirection.rtl,
          style: const TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
          decoration: const InputDecoration(
            hintText: 'اسم التصنيف',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final category = controller.text.trim();
              if (category.isNotEmpty && !_categories.contains(category)) {
                _addCategory(category);
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(fontFamily: AppConstants.fontFamilyPrimary),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
      ),
    );
  }

  Future<void> _addCategory(String category) async {
    try {
      await _storage.addCategory(category);
      setState(() {
        _categories.add(category);
        _selectedCategory = category;
      });
      AppHelpers.showSuccessMessage(context, 'تم إضافة التصنيف بنجاح');
    } catch (e) {
      AppHelpers.showErrorMessage(context, 'حدث خطأ في إضافة التصنيف');
    }
  }

  void _showDeleteDialog() async {
    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف الكتاب',
      message: 'هل أنت متأكد من حذف هذا الكتاب؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      _deleteBook();
    }
  }

  Future<void> _deleteBook() async {
    if (!_isEditMode) return;

    setState(() => _isLoading = true);
    
    try {
      await _bookService.deleteBook(widget.bookToEdit!.id);
      AppHelpers.showSuccessMessage(context, 'تم حذف الكتاب بنجاح');
      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(context, 'حدث خطأ في حذف الكتاب');
    }
  }

  Future<void> _saveBook() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_isEditMode && _selectedPdfPath == null) {
      AppHelpers.showErrorMessage(context, 'يجب اختيار ملف PDF');
      return;
    }

    if (_selectedCategory.isEmpty) {
      AppHelpers.showErrorMessage(context, 'يجب اختيار تصنيف');
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (_isEditMode) {
        // تحديث كتاب موجود
        final updatedBook = widget.bookToEdit!.copyWith(
          title: _titleController.text.trim(),
          author: _authorController.text.trim(),
          description: _descriptionController.text.trim(),
          category: _selectedCategory,
          tags: _tags,
        );
        
        await _bookService.updateBook(updatedBook);
        AppHelpers.showSuccessMessage(context, 'تم تحديث الكتاب بنجاح');
      } else {
        // إضافة كتاب جديد
        await _bookService.addBook(
          title: _titleController.text.trim(),
          author: _authorController.text.trim(),
          description: _descriptionController.text.trim(),
          category: _selectedCategory,
          tags: _tags,
          pdfFilePath: _selectedPdfPath!,
          coverImagePath: _selectedCoverPath,
        );
        AppHelpers.showSuccessMessage(context, 'تم إضافة الكتاب بنجاح');
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      setState(() => _isLoading = false);
      AppHelpers.showErrorMessage(
        context,
        _isEditMode ? 'حدث خطأ في تحديث الكتاب' : 'حدث خطأ في إضافة الكتاب',
      );
    }
  }
}
