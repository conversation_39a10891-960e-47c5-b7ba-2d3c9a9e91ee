// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    UserPreferences(
      fontFamily: json['fontFamily'] as String? ?? 'Cairo',
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      isDarkMode: json['isDarkMode'] as bool? ?? false,
      isNightMode: json['isNightMode'] as bool? ?? false,
      brightness: (json['brightness'] as num?)?.toDouble() ?? 1.0,
      defaultCategory: json['defaultCategory'] as String? ?? 'عام',
      autoBookmark: json['autoBookmark'] as bool? ?? true,
      autoBookmarkInterval:
          (json['autoBookmarkInterval'] as num?)?.toInt() ?? 5,
      readingDirection: $enumDecodeNullable(
              _$ReadingDirectionEnumMap, json['readingDirection']) ??
          ReadingDirection.rightToLeft,
      pageTransition: $enumDecodeNullable(
              _$PageTransitionEnumMap, json['pageTransition']) ??
          PageTransition.slide,
      showReadingProgress: json['showReadingProgress'] as bool? ?? true,
      enableStatistics: json['enableStatistics'] as bool? ?? true,
    );

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) =>
    <String, dynamic>{
      'fontFamily': instance.fontFamily,
      'fontSize': instance.fontSize,
      'isDarkMode': instance.isDarkMode,
      'isNightMode': instance.isNightMode,
      'brightness': instance.brightness,
      'defaultCategory': instance.defaultCategory,
      'autoBookmark': instance.autoBookmark,
      'autoBookmarkInterval': instance.autoBookmarkInterval,
      'readingDirection': _$ReadingDirectionEnumMap[instance.readingDirection]!,
      'pageTransition': _$PageTransitionEnumMap[instance.pageTransition]!,
      'showReadingProgress': instance.showReadingProgress,
      'enableStatistics': instance.enableStatistics,
    };

const _$ReadingDirectionEnumMap = {
  ReadingDirection.rightToLeft: 'rtl',
  ReadingDirection.leftToRight: 'ltr',
};

const _$PageTransitionEnumMap = {
  PageTransition.slide: 'slide',
  PageTransition.fade: 'fade',
  PageTransition.scale: 'scale',
};
